import React, { useEffect, useState } from 'react';
import {
  Table, Button, Modal, Form, InputNumber, Select,
  Space, message, Card, Popconfirm, Tooltip
} from 'antd';
import { EditOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import {
  getUserVip,
  addUserVip,
  updateUserVip,
  deleteUserVip,
  type UserVip,
  type UserVipDTO
} from '../api/userVip';

const { Option } = Select;

const UserVipPage: React.FC = () => {
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<UserVip | null>(null);
  const [form] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();

  // 查询列表
  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await getUserVip();
      console.log("获取的数据：", res.data);  // 打印数据查看结构
      if (res.code === 200) {
        setData(res.data);
      } else {
        messageApi.error(res.message || '查询失败');
      }
    } catch (error) {
      messageApi.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // 打开新增弹窗
  const openAddModal = () => {
    console.log("打开新增VIP弹窗");  // 用于调试，查看是否触发
    setEditingItem(null);  // 重置编辑项
    setIsModalOpen(true);  // 打开弹窗
    form.resetFields();    // 重置表单字段
  };

  // 打开编辑弹窗
  const openEditModal = (item: UserVip) => {
    console.log("编辑VIP:", item);  // 用于调试，查看是否触发
    setEditingItem(item);
    setIsModalOpen(true);
    form.setFieldsValue({
      rechargeBalance: item.rechargeBalance,
      couponId: item.couponId,
      issuedCount: item.issuedCount,
    });
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingItem) {
        // 更新
        const updateData: UserVipDTO = {
          id: editingItem.id,
          ...values,
        };
        const res = await updateUserVip(updateData);
        if (res.code === 200) {
          messageApi.success('更新成功');
          setIsModalOpen(false);
          fetchData();
        } else {
          messageApi.error(res.message || '更新失败');
        }
      } else {
        // 新增
        const res = await addUserVip(values);
        if (res.code === 200) {
          messageApi.success('添加成功');
          setIsModalOpen(false);
          fetchData();
        } else {
          messageApi.error(res.message || '添加失败');
        }
      }
    } catch (error) {
      messageApi.error('操作失败');
    }
  };

  // 删除操作
const handleDelete = async (item: UserVip) => {
  try {
    const res = await deleteUserVip({ id: item.id }); // 传递 id
    console.log("删除响应：", res); // 打印响应，调试用
    if (res.code === 200) {
      messageApi.success('删除成功');
      fetchData(); // 删除成功后重新获取数据
    } else {
      messageApi.error(res.message || '删除失败');
    }
  } catch (error) {
    console.error('删除错误：', error); // 打印错误
    messageApi.error('删除失败');
  }
};

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '充值余额',
      dataIndex: 'rechargeBalance',
      key: 'rechargeBalance',
      width: 120,
      render: (value: number) => `¥${value}`,
    },
    {
      title: '优惠券ID',
      dataIndex: 'couponId',
      key: 'couponId',
      width: 100,
    },
    {
      title: '优惠券标题',
      dataIndex: 'couponTitle',
      key: 'couponTitle',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text || '-'}
        </Tooltip>
      ),
    },
    {
      title: '发放数量',
      dataIndex: 'issuedCount',
      key: 'issuedCount',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: UserVip) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除该记录？"
            onConfirm={() => handleDelete(record)}
            okText="确认"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      {contextHolder}

      <Card
        title="👑 用户VIP管理"
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={openAddModal}>
            新增VIP
          </Button>
        }
      >
        <Table
          rowKey="id"
          dataSource={data}
          columns={columns}
          loading={loading}
          scroll={{ x: 800 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 新增/编辑弹窗 */}
      <Modal
        title={editingItem ? '编辑VIP' : '新增VIP'}
        open={isModalOpen} // 控制 Modal 显示与隐藏
        onCancel={() => setIsModalOpen(false)} // 关闭 Modal
        onOk={handleSubmit} // 提交表单
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="rechargeBalance"
            label="充值余额"
            rules={[
              { required: true, message: '请输入充值余额' },
              { type: 'number', min: 0, message: '余额不能为负数' },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={2}
              placeholder="请输入充值余额"
              addonBefore="¥"
            />
          </Form.Item>

          <Form.Item
            name="couponId"
            label="优惠券ID"
            rules={[
              { required: true, message: '请输入优惠券ID' },
              { type: 'number', min: 1, message: '请输入有效的优惠券ID' },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              placeholder="请输入优惠券ID"
            />
          </Form.Item>

          <Form.Item
            name="issuedCount"
            label="发放数量"
            rules={[
              { required: true, message: '请输入发放数量' },
              { type: 'number', min: 0, message: '发放数量不能为负数' },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="请输入发放数量"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default UserVipPage;

import React, { useState, useEffect, useRef } from 'react'
import { Layout, Menu, Button, message, Badge, notification } from 'antd'
import { Link, useLocation, Outlet, useNavigate } from 'react-router-dom'
import { getAllCleaningItemShopOrder } from '../api/cleaningItemShopOrder'
import { getUserOrders } from '../api/order'
import notificationSound from '../assets/ttsmaker-file-2025-8-7-17-51-8.mp3'

const { Header, Sider, Content } = Layout

const AppLayout: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [unviewedOrderCount, setUnviewedOrderCount] = useState(0)
  const [unviewedUserOrderCount, setUnviewedUserOrderCount] = useState(0)
  const wsRef = useRef<WebSocket | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  // 获取当前用户角色信息
  const accountPermission = localStorage.getItem('accountPermission') // 获取用户权限 (1: 员工, 2: 管理员)

  // 添加调试信息 - 可以在控制台查看权限值
  console.log('当前权限值:', accountPermission, '类型:', typeof accountPermission)

  // 当前激活项
  const selectedKeys = [location.pathname]

  // 默认展开的父级菜单项（受控）
  const [openKeys, setOpenKeys] = useState<string[]>([])

  // 获取路径对应的展开键
  const getOpenKeysFromPath = (pathname: string): string[] => {
    const openKeysMap: { [key: string]: string[] } = {
      '/user-list': ['user-management'],
      '/caregiver-list': ['user-management'],
      '/coupons/create': ['coupon'],
      '/coupons/types': ['coupon'],
      '/coupons/gift-probability': ['coupon'],
      '/user-vip': ['coupon'],
      '/addAccount': ['employee'],
      '/employee-list': ['employee'],
    }
    
    return openKeysMap[pathname] || []
  }

  // 获取未查看清洁用品订单数量
  const fetchUnviewedOrderCount = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const res = await getAllCleaningItemShopOrder({ page: 1, size: 1000 })
      if (res.code === 200) {
        const unviewed = (res.data.records || []).filter(item => item.view === 0).length
        setUnviewedOrderCount(unviewed)
      }
    } catch (error) {
      console.error('获取未查看清洁用品订单数量失败:', error)
    }
  }

  // 获取未查看用户订单数量
  const fetchUnviewedUserOrderCount = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) return

      // 获取所有订单来计算未查看数量（实际项目中可以优化为后端直接返回未查看数量）
      const res = await getUserOrders({
        page: 1,
        size: 10000, // 获取足够多的数据来统计
      })
      if (res.code === 200) {
        // 过滤未查看的订单（view字段为0的订单）
        const records = res.data.records || []
        const unviewed = records.filter((item: any) => item.view === 0).length
        setUnviewedUserOrderCount(unviewed)
        console.log('用户订单未查看数量:', unviewed, '总订单数:', records.length)
      }
    } catch (error) {
      console.error('获取未查看用户订单数量失败:', error)
    }
  }

  // 备用音频播放方案（当MP3播放失败时使用）
  const playNotificationAudio = () => {
    try {
      // 使用Web Audio API创建简单的提示音
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      // 解锁音频上下文
      if (audioContext.state === 'suspended') {
        audioContext.resume()
      }

      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      // 设置音频参数 - 创建一个双音调提示音
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.15)
      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.8, audioContext.currentTime + 0.05) // 调大音量
      gainNode.gain.linearRampToValueAtTime(0.8, audioContext.currentTime + 0.25)
      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.35)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.4)

      console.log('播放备用提示音')
      return true
    } catch (error) {
      console.error('备用音频播放失败:', error)
      return false
    }
  }

// 播放MP3提示音
const playNotificationSound = () => {
  try {
    // 如果已有音频在播放，先停止
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }

    // 创建新的音频实例
    const audio = new Audio(notificationSound)
    audioRef.current = audio

    // 设置音频属性
    audio.volume = 0.9  // 设置音量为90%
    audio.preload = 'auto'

    // 播放音频
    const playPromise = audio.play()

    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          console.log('MP3提示音播放成功')
        })
        .catch((error) => {
          console.error('MP3提示音播放失败:', error)
          // 如果mp3播放失败，使用备用方案
          playNotificationAudio()
        })
    }
  } catch (error) {
    console.error('创建音频实例失败:', error)
    // 使用备用方案
    playNotificationAudio()
  }
}

  // 初始化WebSocket连接
  const initWebSocket = () => {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('userId')
    if (!token || !userId) return

    // 根据后端WebSocket地址调整
    const wsUrl = `ws://192.168.1.67:8081/webSocket/management/${userId}`
    
    try {
      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.onopen = () => {
        console.log('WebSocket连接已建立')
      }

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('收到WebSocket消息:', data)

          // 处理清洁用品订单通知
          if (data.type === 'NEW_ORDER' || data.type === 'shop_order_to_management') {
            handleNewOrderNotification(data)
          }
          // 处理用户订单通知
          else if (data.type === 'NEW_USER_ORDER' || data.type === 'user_order_to_management') {
            handleNewUserOrderNotification(data)
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }

      wsRef.current.onclose = (event) => {
        console.log('WebSocket连接已关闭:', event.code, event.reason)
        // 3秒后尝试重连
        setTimeout(() => {
          if (localStorage.getItem('token')) {
            initWebSocket()
          }
        }, 3000)
      }

      wsRef.current.onerror = (error) => {
        console.error('WebSocket连接错误:', error)
      }
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
    }
  }

  // 处理清洁用品订单通知
  const handleNewOrderNotification = (data: any) => {
  // 增加未查看订单数量
  setUnviewedOrderCount(prev => prev + 1)

  // 播放提示音 - 使用优化后的函数
  playNotificationSound()

  // 显示页面内通知
  notification.open({
    message: '新清洁用品订单提醒',
    description: `收到新的清洁用品订单！订单号：${data.orderNumber || '未知'}`,
    type: 'info',
    placement: 'topRight',
    duration: 5,
    onClick: () => {
      navigate('/cleaning-orders')
    }
  })

  // 显示桌面通知
  if ('Notification' in window && Notification.permission === 'granted') {
    const desktopNotification = new Notification('新清洁用品订单提醒', {
      body: `收到新的清洁用品订单！订单号：${data.orderNumber || '待确认'}`,
      icon: '/favicon.ico',
      tag: 'new-cleaning-order',
      requireInteraction: true
    })

    desktopNotification.onclick = () => {
      window.focus()
      navigate('/cleaning-orders')
      desktopNotification.close()
    }

    setTimeout(() => {
      desktopNotification.close()
    }, 5000)
  }
}
  // 处理用户订单通知
  const handleNewUserOrderNotification = (data: any) => {
    // 增加未查看用户订单数量
    setUnviewedUserOrderCount(prev => prev + 1)

    // 播放MP3提示音
    playNotificationSound()

    // 显示页面内通知
    notification.open({
      message: '新用户订单提醒',
      description: `收到新的用户订单！订单号：${data.orderNumber || '未知'}`,
      type: 'success',
      placement: 'topRight',
      duration: 5,
      onClick: () => {
        navigate('/orders')
      }
    })

    // 显示桌面通知
    if ('Notification' in window && Notification.permission === 'granted') {
      const desktopNotification = new Notification('新用户订单提醒', {
        body: `收到新的用户订单！订单号：${data.orderNumber || '待确认'}`,
        icon: '/favicon.ico', // 可以设置图标
        tag: 'new-user-order', // 防止重复通知
        requireInteraction: true // 需要用户交互才关闭
      })

      // 点击桌面通知时的处理
      desktopNotification.onclick = () => {
        window.focus() // 聚焦到浏览器窗口
        navigate('/orders')
        desktopNotification.close()
      }

      // 5秒后自动关闭
      setTimeout(() => {
        desktopNotification.close()
      }, 5000)
    }
  }

  // 路径变化时更新展开的菜单项
  useEffect(() => {
    const newOpenKeys = getOpenKeysFromPath(location.pathname)
    setOpenKeys(newOpenKeys)
  }, [location.pathname])

  // 处理菜单展开/收起事件
  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys)
  }

  // 初始化数据和WebSocket连接
  useEffect(() => {
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }

    // 获取未查看订单数量
    fetchUnviewedOrderCount()
    fetchUnviewedUserOrderCount()
    initWebSocket()

    // 监听刷新事件
    const handleRefresh = () => {
      fetchUnviewedOrderCount()
      fetchUnviewedUserOrderCount()
    }

    // 监听测试提示音事件
    const handleTestSound = () => {
      playNotificationSound()
    }

    window.addEventListener('refreshUnviewedCount', handleRefresh)
    window.addEventListener('refreshUnviewedUserOrderCount', handleRefresh)
    window.addEventListener('testNotificationSound', handleTestSound)

    // 设置定时刷新未查看订单数量（每30秒刷新一次）
    const refreshInterval = setInterval(() => {
      fetchUnviewedOrderCount()
      fetchUnviewedUserOrderCount()
    }, 30000)

    // 清理函数
    return () => {
      window.removeEventListener('refreshUnviewedCount', handleRefresh)
      window.removeEventListener('refreshUnviewedUserOrderCount', handleRefresh)
      window.removeEventListener('testNotificationSound', handleTestSound)
      clearInterval(refreshInterval)
      if (wsRef.current) {
        wsRef.current.close()
      }
      // 清理音频
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current = null
      }
    }
  }, [])

  // 退出登录处理
  const handleLogout = () => {
    // 关闭WebSocket连接
    if (wsRef.current) {
      wsRef.current.close()
    }
    
    localStorage.removeItem('token')
    localStorage.removeItem('accountPermission')
    message.success('退出登录成功')
    navigate('/login')
  }

  // 判断是否为管理员 - 更严格的权限检查
  const isAdmin = accountPermission === '2' || Number(accountPermission) === 2

  return (
    <Layout>
      <Sider
        width={200}
        style={{
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 1000,
          background: '#fff',
          overflowY: 'auto',
          borderRight: 0
        }}
      >
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          openKeys={openKeys}
          onOpenChange={handleOpenChange}
          style={{ height: '100%' }}
        >
          <Menu.Item key="/dashboard">
            <Link to="/dashboard">📊 数据看板</Link>
          </Menu.Item>

          {/* 用户管理：所有角色都能看到 */}
          <Menu.SubMenu key="user-management" title="👤 用户管理">
            <Menu.Item key="/user-list">
              <Link to="/user-list">用户列表</Link>
            </Menu.Item>
            <Menu.Item key="/caregiver-list">
              <Link to="/caregiver-list">阿姨列表</Link>
            </Menu.Item>
          </Menu.SubMenu>

          <Menu.Item key="/orders">
            <Badge count={unviewedUserOrderCount} size="small" offset={[10, 0]}>
              <Link to="/orders">📦 订单管理</Link>
            </Badge>
          </Menu.Item>

          <Menu.Item key="/cleaning-orders">
            <Badge count={unviewedOrderCount} size="small" offset={[10, 0]}>
              <Link to="/cleaning-orders">🧽 清洁用品订单</Link>
            </Badge>
          </Menu.Item>

          {/* 根据权限控制菜单项 - 使用更严格的权限检查 */}
          {isAdmin && (
          <>
              <Menu.SubMenu key="coupon" title="🎫 优惠券管理">
                <Menu.Item key="/coupons/create">
                  <Link to="/coupons/create">创建优惠券</Link>
                </Menu.Item>
                <Menu.Item key="/coupons/types">
                  <Link to="/coupons/types">查询优惠券类型</Link>
                </Menu.Item>
                <Menu.Item key="/coupons/gift-probability">
                  <Link to="/coupons/gift-probability">优惠券发放概率</Link>
                </Menu.Item>
                <Menu.Item key="/user-vip">
                  <Link to="/user-vip">用户充值VIP</Link>
                </Menu.Item>
              </Menu.SubMenu>
              <Menu.Item key="/notification">
                <Link to="/notification">📢 系统通知</Link>
              </Menu.Item>
              <Menu.Item key="/sys-picture">
                <Link to="/sys-picture">🖼️ 首页图片</Link>
              </Menu.Item>
              <Menu.Item key="/commission-rate">
                <Link to="/commission-rate">📈 抽成比例</Link>
              </Menu.Item>
              
              <Menu.SubMenu key="employee" title="👤 员工管理">
              <Menu.Item key="/addAccount">
                <Link to="/addAccount">📝 注册账号</Link>
              </Menu.Item>
              <Menu.Item key="/employee-list">
                <Link to="/employee-list">🔍 员工信息</Link>
              </Menu.Item>
              </Menu.SubMenu>
              <Menu.Item key="/salary-management">
                <Link to="/salary-management">💰 工资管理</Link>
              </Menu.Item>
            </>
          )}

          <Menu.Item key="/caregiver">
            <Link to="/caregiver-audit">🧍‍♀️ 阿姨审核</Link>
          </Menu.Item>

          <Menu.Item key="/complaints">
            <Link to="/complaints">📝 投诉管理</Link>
          </Menu.Item>

          {/* 调试信息 - 生产环境请删除 */}
          {process.env.NODE_ENV === 'development' && (
            <>
              <Menu.Item key="/debug" style={{ color: '#999', fontSize: '12px' }}>
                权限: {accountPermission} ({typeof accountPermission})
              </Menu.Item>
              <Menu.Item key="/test-notification" style={{ color: '#999', fontSize: '12px' }}>
                <Button
                  size="small"
                  type="link"
                  onClick={() => {
                    // 测试用户订单通知
                    handleNewUserOrderNotification({
                      type: 'NEW_USER_ORDER',
                      orderNumber: 'TEST' + Date.now()
                    })
                  }}
                >
                  测试用户订单通知
                </Button>
              </Menu.Item>
            </>
          )}
        </Menu>
      </Sider>

      {/* 页面右侧内容区 */}
      <Layout style={{ marginLeft: 200 }}>
        <Header style={{ background: '#fff', padding: '0 16px', fontSize: 18, position: 'relative' }}>
          企业后台管理系统
          
          {/* 显示当前用户权限 - 调试用 */}
          <span style={{ marginLeft: 20, fontSize: 14, color: '#666' }}>
            当前权限: {isAdmin ? '管理员' : '员工'}
          </span>

          {/* WebSocket连接状态指示器 */}
          <span style={{ 
            marginLeft: 20, 
            fontSize: 12, 
            color: wsRef.current?.readyState === WebSocket.OPEN ? '#52c41a' : '#ff4d4f' 
          }}>
            {wsRef.current?.readyState === WebSocket.OPEN ? '🟢 已连接' : '🔴 未连接'}
          </span>

          {/* 退出登录按钮 */}
          <Button
            type="primary"
            onClick={handleLogout}
            style={{
              position: 'absolute',
              right: 16,
              top: '50%',
              transform: 'translateY(-50%)',  // 垂直居中
            }}
          >
            退出登录
          </Button>
        </Header>

        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            background: 'rgba(255, 255, 255, 0.75)',
            backdropFilter: 'blur(8px)',
            borderRadius: 8,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  )
}

export default AppLayout
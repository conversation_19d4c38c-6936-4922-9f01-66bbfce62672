import React, { useState, useEffect, useRef } from 'react'
import { Layout, Menu, Button, message, Badge, notification } from 'antd'
import { Link, useLocation, Outlet, useNavigate } from 'react-router-dom'
import { getAllCleaningItemShopOrder } from '../api/cleaningItemShopOrder'

const { Header, Sider, Content } = Layout

const AppLayout: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [unviewedOrderCount, setUnviewedOrderCount] = useState(0)
  const wsRef = useRef<WebSocket | null>(null)

  // 获取当前用户角色信息
  const accountPermission = localStorage.getItem('accountPermission') // 获取用户权限 (1: 员工, 2: 管理员)

  // 添加调试信息 - 可以在控制台查看权限值
  console.log('当前权限值:', accountPermission, '类型:', typeof accountPermission)

  // 当前激活项
  const selectedKeys = [location.pathname]

  // 默认展开的父级菜单项（受控）
  const [openKeys, setOpenKeys] = useState<string[]>([])

  // 获取路径对应的展开键
  const getOpenKeysFromPath = (pathname: string): string[] => {
    const openKeysMap: { [key: string]: string[] } = {
      '/user-list': ['user-management'],
      '/caregiver-list': ['user-management'],
      '/coupons/create': ['coupon'],
      '/coupons/types': ['coupon'],
      '/coupons/gift-probability': ['coupon'],
      '/user-vip': ['coupon'],
      '/addAccount': ['employee'],
      '/employee-list': ['employee'],
    }
    
    return openKeysMap[pathname] || []
  }

  // 获取未查看订单数量
  const fetchUnviewedOrderCount = async () => {
    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const res = await getAllCleaningItemShopOrder({ page: 1, size: 1000 })
      if (res.code === 200) {
        const unviewed = (res.data.records || []).filter(item => item.view === 0).length
        setUnviewedOrderCount(unviewed)
      }
    } catch (error) {
      console.error('获取未查看订单数量失败:', error)
    }
  }

  // 初始化WebSocket连接
  const initWebSocket = () => {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('userId')
    if (!token || !userId) return

    // 根据后端WebSocket地址调整
    const wsUrl = `ws://************:8081/webSocket/management/${userId}`
    
    try {
      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.onopen = () => {
        console.log('WebSocket连接已建立')
      }

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('收到WebSocket消息:', data)

          // 处理新订单通知
          if (data.type === 'NEW_ORDER' || data.type === 'shop_order_to_management') {
            handleNewOrderNotification(data)
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }

      wsRef.current.onclose = (event) => {
        console.log('WebSocket连接已关闭:', event.code, event.reason)
        // 3秒后尝试重连
        setTimeout(() => {
          if (localStorage.getItem('token')) {
            initWebSocket()
          }
        }, 3000)
      }

      wsRef.current.onerror = (error) => {
        console.error('WebSocket连接错误:', error)
      }
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
    }
  }

  // 处理新订单通知
  const handleNewOrderNotification = (data: any) => {
    // 增加未查看订单数量
    setUnviewedOrderCount(prev => prev + 1)

    // 显示页面内通知
    notification.open({
      message: '新订单提醒',
      description: `收到新的清洁用品订单！订单号：${data.orderNumber || '未知'}`,
      type: 'info',
      placement: 'topRight',
      duration: 5,
      onClick: () => {
        navigate('/cleaning-orders')
      }
    })

    // 显示桌面通知
    if ('Notification' in window && Notification.permission === 'granted') {
      const desktopNotification = new Notification('新订单提醒', {
        body: `收到新的清洁用品订单！订单号：${data.orderNumber || '待确认'}`,
        icon: '/favicon.ico', // 可以设置图标
        tag: 'new-order', // 防止重复通知
        requireInteraction: true // 需要用户交互才关闭
      })

      // 点击桌面通知时的处理
      desktopNotification.onclick = () => {
        window.focus() // 聚焦到浏览器窗口
        navigate('/cleaning-orders')
        desktopNotification.close()
      }

      // 5秒后自动关闭
      setTimeout(() => {
        desktopNotification.close()
      }, 5000)
    }
  }

  // 路径变化时更新展开的菜单项
  useEffect(() => {
    const newOpenKeys = getOpenKeysFromPath(location.pathname)
    setOpenKeys(newOpenKeys)
  }, [location.pathname])

  // 处理菜单展开/收起事件
  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys)
  }

  // 初始化数据和WebSocket连接
  useEffect(() => {
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
    
    // fetchUnviewedOrderCount()
    initWebSocket()

    // 监听刷新事件
    const handleRefresh = () => {
      // fetchUnviewedOrderCount()
    }
    
    window.addEventListener('refreshUnviewedCount', handleRefresh)

    // 清理函数
    return () => {
      window.removeEventListener('refreshUnviewedCount', handleRefresh)
      if (wsRef.current) {
        wsRef.current.close()
      }
    }
  }, [])

  // 退出登录处理
  const handleLogout = () => {
    // 关闭WebSocket连接
    if (wsRef.current) {
      wsRef.current.close()
    }
    
    localStorage.removeItem('token')
    localStorage.removeItem('accountPermission')
    message.success('退出登录成功')
    navigate('/login')
  }

  // 判断是否为管理员 - 更严格的权限检查
  const isAdmin = accountPermission === '2' || Number(accountPermission) === 2

  return (
    <Layout>
      <Sider
        width={200}
        style={{
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 1000,
          background: '#fff',
          overflowY: 'auto',
          borderRight: 0
        }}
      >
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          openKeys={openKeys}
          onOpenChange={handleOpenChange}
          style={{ height: '100%' }}
        >
          <Menu.Item key="/dashboard">
            <Link to="/dashboard">📊 数据看板</Link>
          </Menu.Item>

          {/* 用户管理：所有角色都能看到 */}
          <Menu.SubMenu key="user-management" title="👤 用户管理">
            <Menu.Item key="/user-list">
              <Link to="/user-list">用户列表</Link>
            </Menu.Item>
            <Menu.Item key="/caregiver-list">
              <Link to="/caregiver-list">阿姨列表</Link>
            </Menu.Item>
          </Menu.SubMenu>

          <Menu.Item key="/orders">
            <Link to="/orders">📦 订单管理</Link>
          </Menu.Item>

          <Menu.Item key="/cleaning-orders">
            <Badge count={unviewedOrderCount} size="small" offset={[10, 0]}>
              <Link to="/cleaning-orders">🧽 清洁用品订单</Link>
            </Badge>
          </Menu.Item>

          {/* 根据权限控制菜单项 - 使用更严格的权限检查 */}
          {isAdmin && (
          <>
              <Menu.SubMenu key="coupon" title="🎫 优惠券管理">
                <Menu.Item key="/coupons/create">
                  <Link to="/coupons/create">创建优惠券</Link>
                </Menu.Item>
                <Menu.Item key="/coupons/types">
                  <Link to="/coupons/types">查询优惠券类型</Link>
                </Menu.Item>
                <Menu.Item key="/coupons/gift-probability">
                  <Link to="/coupons/gift-probability">优惠券发放概率</Link>
                </Menu.Item>
                <Menu.Item key="/user-vip">
                  <Link to="/user-vip">用户充值VIP</Link>
                </Menu.Item>
              </Menu.SubMenu>
              <Menu.Item key="/notification">
                <Link to="/notification">📢 系统通知</Link>
              </Menu.Item>
              <Menu.Item key="/sys-picture">
                <Link to="/sys-picture">🖼️ 首页图片</Link>
              </Menu.Item>
              <Menu.Item key="/commission-rate">
                <Link to="/commission-rate">📈 抽成比例</Link>
              </Menu.Item>
              
              <Menu.SubMenu key="employee" title="👤 员工管理">
              <Menu.Item key="/addAccount">
                <Link to="/addAccount">📝 注册账号</Link>
              </Menu.Item>
              <Menu.Item key="/employee-list">
                <Link to="/employee-list">🔍 员工信息</Link>
              </Menu.Item>
              </Menu.SubMenu>
              <Menu.Item key="/salary-management">
                <Link to="/salary-management">💰 工资管理</Link>
              </Menu.Item>
            </>
          )}

          <Menu.Item key="/caregiver">
            <Link to="/caregiver-audit">🧍‍♀️ 阿姨审核</Link>
          </Menu.Item>

          <Menu.Item key="/complaints">
            <Link to="/complaints">📝 投诉管理</Link>
          </Menu.Item>

          {/* 调试信息 - 生产环境请删除 */}
          {process.env.NODE_ENV === 'development' && (
            <Menu.Item key="/debug" style={{ color: '#999', fontSize: '12px' }}>
              权限: {accountPermission} ({typeof accountPermission})
            </Menu.Item>
          )}
        </Menu>
      </Sider>

      {/* 页面右侧内容区 */}
      <Layout style={{ marginLeft: 200 }}>
        <Header style={{ background: '#fff', padding: '0 16px', fontSize: 18, position: 'relative' }}>
          企业后台管理系统
          
          {/* 显示当前用户权限 - 调试用 */}
          <span style={{ marginLeft: 20, fontSize: 14, color: '#666' }}>
            当前权限: {isAdmin ? '管理员' : '员工'}
          </span>

          {/* WebSocket连接状态指示器 */}
          <span style={{ 
            marginLeft: 20, 
            fontSize: 12, 
            color: wsRef.current?.readyState === WebSocket.OPEN ? '#52c41a' : '#ff4d4f' 
          }}>
            {wsRef.current?.readyState === WebSocket.OPEN ? '🟢 已连接' : '🔴 未连接'}
          </span>

          {/* 退出登录按钮 */}
          <Button
            type="primary"
            onClick={handleLogout}
            style={{
              position: 'absolute',
              right: 16,
              top: '50%',
              transform: 'translateY(-50%)',  // 垂直居中
            }}
          >
            退出登录
          </Button>
        </Header>

        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            background: 'rgba(255, 255, 255, 0.75)',
            backdropFilter: 'blur(8px)',
            borderRadius: 8,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  )
}

export default AppLayout
import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import 'antd/dist/reset.css'
import './index.css'
import App from './App'
import { ConfigProvider, App as AntdApp } from 'antd'
import { theme } from './theme'
import zhCN from 'antd/locale/zh_CN'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <ConfigProvider 
        theme={theme}
        locale={zhCN}
        getPopupContainer={() => document.body}
      >
        <AntdApp>
          <App />
        </AntdApp>
      </ConfigProvider>
    </BrowserRouter>
  </React.StrictMode>
)


import ApiClient from "./api"

export interface CleaningItemShopOrder {
  id: number
  orderNumber: string
  caregiverId: number
  totalPrice: number
  addressId: number
  fullAddress:string
  status: number
  paymentMethod: number
  payOrderNumber: string
  payTime: string
  remark: string
  createTime: string
  updateTime: string
  view: number
  caregiverName: string
  itemName: string
  unitPrice: number
  quantity: number
  purchasePrice: number
}

export interface CleaningItemShopOrderDTO {
  page?: number
  size?: number
  id?: number
  view?: number
  orderNumber?: string
  caregiverId?: number
  status?: number
  paymentMethod?: number
}

export interface CleaningItemShopOrderResponse {
  records: CleaningItemShopOrder[]
  total: number
  size: number
  current: number
  pages: number
}

// 获取所有清洁用品订单（分页）
export function getAllCleaningItemShopOrder(params: CleaningItemShopOrderDTO) {
  return ApiClient.get<CleaningItemShopOrderResponse>('cleaningItemShopOrder/getAllCleaningItemShopOrder', params)
}

// 获取单个清洁用品订单
export function getCleaningItemShopOrder(data: CleaningItemShopOrderDTO) {
  return ApiClient.post<CleaningItemShopOrder>('cleaningItemShopOrder/getCleaningItemShopOrder', data)
}

// 修改订单状态
export function modifyOrderStatus(data: CleaningItemShopOrderDTO) {
  return ApiClient.put<number>('cleaningItemShopOrder/modifyOrderStatus', data)
}


import ApiClient from "./api"

export interface UserVip {
  id: number
  rechargeBalance: number
  couponId: number
  issuedCount: number
  couponTitle?: string
  couponDescription?: string
}

export interface UserVipDTO {
  id?: number
  rechargeBalance?: number
  couponId?: number
  issuedCount?: number
}

export function getUserVip() {
  return ApiClient.get<{ code: number; message: string; data: UserVip }>(
    'userVip/getUserVip'
  )
}

export function getUserVipByVipId(data: UserVipDTO) {
  return ApiClient.get<{ code: number; message: string; data: UserVip }>(
    'userVip/getUserVipByVipId',
    data
  )
}

export function addUserVip(data: UserVipDTO) {
  return ApiClient.post<{ code: number; message: string; data: number }>(
    'userVip/addUserVip',
    data
  )
}

export function updateUserVip(data: UserVipDTO) {
  return ApiClient.put<{ code: number; message: string; data: number }>(
    'userVip/updateUserVip',
    data
  )
}

export function deleteUserVip(data: any) {
  return ApiClient.delete<{ code: number; message: string; data: number }>(
    'userVip/deleteUserVip',
    data
  )
}
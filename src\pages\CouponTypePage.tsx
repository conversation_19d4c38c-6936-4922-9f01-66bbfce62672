import React, { useEffect, useState } from 'react'
import {
  Table, Button, Modal, Form, Input, InputNumber,
  Space, message, Card, Popconfirm, Tooltip, Alert,
  Select
} from 'antd'
import apiClient from '../api/api'
import { CloseOutlined, CheckCircleOutlined } from '@ant-design/icons'

interface Coupon {
  couponId: number
  title: string
  description: string
  fullAmount: number
  discountValue: number
  maxDiscount: number
  validDay: number
  limitCount: number
  validStartTime: string
  validEndTime: string
  discountType: number
}

interface DistributeNotification {
  visible: boolean
  couponTitle: string
  couponId: number
  timestamp: string
}

const CouponTypePage: React.FC = () => {
  const [data, setData] = useState<Coupon[]>([])
  const [loading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [pageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [form] = Form.useForm()
  const discountType = Form.useWatch('discountType', form)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<Coupon | null>(null)
  const [distributeNotification, setDistributeNotification] = useState<DistributeNotification>({
    visible: false,
    couponTitle: '',
    couponId: 0,
    timestamp: ''
  })

  // 查询列表
  const fetchData = async () => {
    setLoading(true)
    try {
      const res = await apiClient.post<any>('coupon/getCoupon', {
        page,
        size: pageSize,
        isDelete: 0
      })
      const result = res.data
      setData(result.records)
      setTotal(result.total)
    } catch {
      message.error('加载失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [page])

  // 打开编辑弹窗
  const openModal = (item: Coupon) => {
    setEditingItem(item)
    setIsModalOpen(true)
    form.setFieldsValue(item)
  }

  // 提交更新
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      const res = await apiClient.put('coupon/putCoupon', {
        couponId: editingItem?.couponId,
        ...values
      })
      if (res.code === 200) {
        message.success('更新成功')
        setIsModalOpen(false)
        fetchData()
      } else {
        message.error(res.message || '更新失败')
      }
    } catch {
      // 校验失败
    }
  }

   // 删除优惠券
   const handleDelete = async (couponId: number) => {
    try {
      const res = await apiClient.put('coupon/deleteCoupon', {
        couponId
      })
      if (res.code === 200) {
        message.success('删除成功')
        fetchData()
      } else {
        message.error(res.message || '删除失败')
      }
    } catch {
      message.error('删除失败')
    }
  }

  //下发优惠券
  const handleDistribute = async (couponId: number) => {
    try {
      const res = await apiClient.post('userCoupon/distributeCoupon', {
        couponId
      })
      if (res.code === 200) {
        message.success('优惠券下发成功')
        
        // 找到当前优惠券信息
        const currentCoupon = data.find(item => item.couponId === couponId)
        
        // 显示页面提示
        setDistributeNotification({
          visible: true,
          couponTitle: currentCoupon?.title || '未知优惠券',
          couponId: couponId,
          timestamp: new Date().toLocaleString('zh-CN')
        })
        
        // 5秒后自动关闭提示
        setTimeout(() => {
          setDistributeNotification(prev => ({ ...prev, visible: false }))
        }, 3000)
        
      } else {
        message.error(res.message || '下发失败')
      }
    } catch {
      message.error('下发失败')
    }
  }

  // 手动关闭提示
  const closeNotification = () => {
    setDistributeNotification(prev => ({ ...prev, visible: false }))
  }

  return (
    <div style={{ padding: 24 }}>
      {/* 下发成功提示 */}
      {distributeNotification.visible && (
        <Alert
          message={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                <div>
                  <strong>优惠券下发成功！</strong>
                  <div style={{ fontSize: '12px', color: '#666', marginTop: 2 }}>
                    优惠券「{distributeNotification.couponTitle}」(ID: {distributeNotification.couponId}) 
                    已于 {distributeNotification.timestamp} 成功下发
                  </div>
                </div>
              </div>
              <Button 
                type="text" 
                size="small" 
                icon={<CloseOutlined />} 
                onClick={closeNotification}
              />
            </div>
          }
          type="success"
          showIcon={false}
          style={{ 
            marginBottom: 16,
            border: '1px solid #b7eb8f',
            backgroundColor: '#f6ffed'
          }}
        />
      )}

      <Card title="优惠券类型列表">
        <Table
          rowKey="couponId"
          dataSource={data}
          loading={loading}
          scroll={{
            x: 1300,  // 横向滚动（必须设置所有列宽）
            // y: 800    // 固定表格高度，滚动条始终可见
          }}
          pagination={{
            current: page,
            pageSize,
            total,
            onChange: (p) => setPage(p)
          }}
        >
          <Table.Column title="ID" dataIndex="couponId" />
          <Table.Column title="标题" dataIndex="title" width={150} />
          <Table.Column
            title="描述"
            dataIndex="description"
            ellipsis={{ showTitle: false }}
            render={(text: string) => (
              <Tooltip placement="topLeft" title={text}>
                {text}
              </Tooltip>
            )}
          />
          <Table.Column 
          title="折扣类型" 
          dataIndex="discountType" 
          render={(value) => {
              if (value === 1) return '固定金额';
              if (value === 2) return '百分比';
              return '-';
            }}
          />
          <Table.Column title="满金额" dataIndex="fullAmount" />
          <Table.Column title="折扣值" dataIndex="discountValue" />
          <Table.Column title="最大折扣值" dataIndex="maxDiscount"/>
          <Table.Column title="有效天数" dataIndex="validDay" width={100} />
          <Table.Column title="限制数量" dataIndex="limitCount" width={100} />
          <Table.Column title="开始时间" dataIndex="validStartTime" width={200} />
          <Table.Column title="结束时间" dataIndex="validEndTime" width={200} />
          <Table.Column
          fixed="right"
          width={150}
            title="操作"
            render={(_, record: Coupon) => (
              <Space>
                <Button type="link" onClick={() => openModal(record)}>编辑</Button>
                <Popconfirm
                  title="确认删除该优惠券？"
                  onConfirm={() => handleDelete(record.couponId)}
                  okText="确认"
                  cancelText="取消"
                >
                  <Button type="link" danger>删除</Button>
                </Popconfirm>
                <Button type="link" onClick={() => handleDistribute(record.couponId)}>下发</Button>
              </Space>
            )}
          />
        </Table>
      </Card>

      {/* 编辑弹窗 */}
      <Modal
        title="编辑优惠券"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleSubmit}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item name="title" label="标题" rules={[{ required: true, message: '请输入标题' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="description" label="描述" rules={[{ required: true, message: '请输入描述' }]}>
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="discountType"
            label="折扣类型"
            rules={[{required:true,message:'请选择折扣类型'}]}
          >
            <Select placeholder = "请选择折扣类型">
              <Select.Option value={1}>固定金额</Select.Option>
              <Select.Option value={2}>百分比</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="fullAmount" label="满金额" rules={[{ required: true, message: '请输入满金额' }]}>
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="discountValue" label="折扣值" rules={[{ required: true, message: '请输入折扣值' }]}>
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item 
            name="maxDiscount"
            label="最大折扣" 
            rules={[{ required: discountType === 2,message: '请输入最大折扣' }]}
            >
               <InputNumber
              style={{ width: '100%' }}
              disabled={discountType === 1}
              placeholder={discountType === 1 ? '固定金额无需填写' : '请输入最大折扣'}
            />
          </Form.Item>
          <Form.Item name="validDay" label="有效天数" rules={[{ required: true, message: '请输入有效天数' }]}>
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="limitCount" label="限制数量" rules={[{ required: true, message: '请输入限制数量' }]}>
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default CouponTypePage

// import ApiClient from 'ApiClient'
import ApiClient from "./api"


export interface Caregiver {
  permissions: any
  caregiverId: number
  name: string
  age: number
  gender: number
  phoneNumber: string
  status: number
  idCardFront: string
  idCardBack: string
  healthCertificate: string
  examStatus: number
  score: number
}

export interface CaregiverDTO {
  page?: number
  size?: number
}


export interface CaregiverAuditImages {
  idCardFront: string
  idCardBack: string
  healthCertificate: string
}

export function getCaregivers(data: CaregiverDTO) {
  return ApiClient.post<any>(
    'caregiver/getCaregiver',
    data
  )
}

export function getAuditImages(data: { caregiverId: number }) {
  return ApiClient.post<{ code: number; message: string; data: CaregiverAuditImages }>(
    'caregiver/audit-images',
    data
  )
}

export function getCaregiverById(data: { caregiverId?: string,name?: string }) {
  return ApiClient.post<{ code: number; message: string; data: Caregiver[] }>(
    'caregiver/getCaregiverId',
    data
  )
}
// 查询阿姨权限
export function getCaregiverPermission(data: { caregiverId: number }) {
  return ApiClient.get<{ code: number; message: string; data: any }>(
    `caregiverServiceType/queryPermission?caregiverId=${data.caregiverId}`
  )
}

// 添加阿姨权限
export function addCaregiverPermission(data:any) {
  return ApiClient.post<any>(
    'caregiverServiceType/addPermission',
    data
  )
}

// 删除阿姨权限
export function deleteCaregiverPermission(data:{id:number}) {
  return ApiClient.delete<{ code: number; message: string }>(
    'caregiverServiceType/deletePermission',
    data
  )
}

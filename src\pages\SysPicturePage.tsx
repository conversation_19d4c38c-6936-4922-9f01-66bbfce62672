import React, { useEffect, useState } from 'react'
import {
  Table, Image, Card, Button,
  Popconfirm, Modal, Form, Input, Upload, Select,App
} from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import apiClient from '../api/api'

interface SysPicture {
  pictureId: number
  pictureName: string
  pictureType: number
  jumpLink: string
  downloadAddress: string
  signedUrl?: string
}

const SysPicturePage: React.FC = () => {
  const { message } = App.useApp()
  const [data, setData] = useState<SysPicture[]>([])
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()
  const [uploadForm] = Form.useForm()

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isUploadModalVisible, setIsUploadModalVisible] = useState(false)

  const [editingItem, setEditingItem] = useState<SysPicture | null>(null)
  const [fileList, setFileList] = useState<any[]>([])
  const [uploadFileList, setUploadFileList] = useState<any[]>([])

  // 查询数据
  const fetchPictures = async () => {
    setLoading(true)
    try {
      const res = await apiClient.post<any>('sysPicture/getSysPicture')
      if (res.code === 200) {
        setData(res.data)
      }
    } catch {
      message.error('获取图片失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPictures()
  }, [])

  // 删除图片
  const handleDelete = async (record: SysPicture) => {
    try {
      const res = await apiClient.delete('sysPicture/deleteSysPicture', {
        pictureId: record.pictureId
      })
      if (res.code === 200) {
        message.success('删除成功')
        fetchPictures()
      }
    } catch {
      message.error('删除失败')
    }
  }

  // 打开编辑
  const openEditModal = (record: SysPicture) => {
    setEditingItem(record)
    setFileList([])
    form.setFieldsValue(record)
    setIsModalOpen(true)
  }

  // 执行编辑提交
  const handleUpdate = async () => {
    try {
      const values = await form.validateFields()
      const formData = new FormData()
      formData.append('pictureId', editingItem?.pictureId.toString() || '')
      formData.append('pictureName', values.pictureName)
      formData.append('pictureType', values.pictureType.toString())
      formData.append('jumpLink', values.jumpLink || '')
      
      if (fileList.length > 0) {
        formData.append('file', fileList[0].originFileObj)
      }

      const res = await apiClient.put('sysPicture/update', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      if (res.code === 200) {
        message.success('修改成功')
        setIsModalOpen(false)
        fetchPictures()
      }
    } catch {
      message.error('修改失败')
    }
  }

  // 表格列定义
  const columns = [
    { title: '图片ID', dataIndex: 'pictureId' },
    { title: '图片名称', dataIndex: 'pictureName' },
    {
      title: '图片类型',
      dataIndex: 'pictureType',
      render: (type: number) => {
        if (type === 1) return '用户头像';   // 类型 1
        if (type === 2) return '阿姨头像';   // 类型 2
        if (type === 3) return '首页广告';   // 类型 3
        return '未知';                        // 其他情况
      }
    },
    {
      title: '跳转链接',
      dataIndex: 'jumpLink',
      render: (link: string) => link ? <a href={link} target="_blank" rel="noreferrer">{link}</a> : '-'
    },
    {
      title: '图片预览',
      dataIndex: 'downloadAddress',
      render: (url: string) => (
        <Image
          width={100}
          src={url}
          alt="图片"
          fallback="https://via.placeholder.com/100x100?text=无图"
        />
      )
    },
    {
      title: '操作',
      render: (_: any, record: SysPicture) => (
        <>
          <Button type="link" onClick={() => openEditModal(record)}>编辑</Button>
          <Popconfirm title="确认删除？" onConfirm={() => handleDelete(record)}>
            <Button danger type="link">删除</Button>
          </Popconfirm>
        </>
      )
    }
  ]

  return (
    <Card
      title="系统图片列表"
      extra={<Button type="primary" onClick={() => setIsUploadModalVisible(true)}>上传图片</Button>}
    >
      <Table
        rowKey="pictureId"
        loading={loading}
        columns={columns}
        dataSource={data}
        pagination={{ pageSize: 10 }}
      />

      {/* 编辑弹窗 */}
      <Modal
        title="编辑图片"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleUpdate}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item name="pictureName" label="图片名称" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="pictureType" label="图片类型" rules={[{ required: true }]}>
          <Select options={[
              { value: 1, label: '用户头像' },
              { value: 2, label: '阿姨头像' },
              { value: 3, label: '首页广告' },
            ]} />
          </Form.Item>
          <Form.Item name="jumpLink" label="跳转链接">
            <Input />
          </Form.Item>
          <Form.Item label="上传新图片（可选）">
            <Upload
              beforeUpload={() => false}
              fileList={fileList}
              onChange={({ fileList }) => setFileList(fileList)}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>选择图片</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>

      {/* 上传弹窗 */}
      <Modal
        title="上传系统图片"
        open={isUploadModalVisible}
        onCancel={() => setIsUploadModalVisible(false)}
        onOk={() => uploadForm.submit()}
        destroyOnClose
      >
        <Form
          form={uploadForm}
          layout="vertical"
          onFinish={async (values) => {
            const formData = new FormData()
            formData.append('pictureName', values.pictureName)
            formData.append('pictureType', values.pictureType)
            formData.append('jumpLink', values.jumpLink || '')
            if (uploadFileList.length > 0) {
              formData.append('file', uploadFileList[0].originFileObj)
            }

            try {
              const res = await apiClient.post('/sysPicture/upload', formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
              })
              if (res.code === 200) {
                message.success('上传成功')
                setIsUploadModalVisible(false)
                fetchPictures()
              } else {
                message.error('上传失败：' + res.message)
              }
            } catch (err: any) {
              message.error('上传异常：' + err.message)
            }
          }}
        >
          <Form.Item name="pictureName" label="图片名称" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="pictureType" label="图片类型" rules={[{ required: true }]}>
            <Select options={[{ value: 1, label: '用户头像' }, { value: 2, label: '阿姨头像' }, { value: 3, label: '首页广告' }]} />
          </Form.Item>
          <Form.Item name="jumpLink" label="跳转链接">
            <Input />
          </Form.Item>
          <Form.Item label="上传图片" rules={[{ required: true }]}>
            <Upload
              beforeUpload={() => false}
              fileList={uploadFileList}
              onChange={({ fileList }) => setUploadFileList(fileList)}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>选择图片</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}

export default SysPicturePage

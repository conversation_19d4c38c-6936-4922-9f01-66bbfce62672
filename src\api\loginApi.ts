import ApiClient from "./api"

// 登录请求参数
export interface LoginParams {
  username: string
  password: string
}

// 登录响应结构
export interface LoginResponse {
  code: number
  message: string
  data: {
    tokenName: string
    tokenValue: string
    accountPermission: number
    id: number  // 添加用户ID字段
  }
}

// 发起请求
export const login = async (params: LoginParams) => {
  const res = await ApiClient.post<any>('adminUser/login', params)
  return res
}

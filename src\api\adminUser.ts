import ApiClient from "./api";

// 定义查询员工返回的数据类型
interface EmployeeResponse {
  records: AdminUser[];
  total: number;
}

// 定义删除员工返回的数据类型
interface DeleteResponse {
  code: number;
  message: string;
}

// 定义员工的类型
interface AdminUser {
  id: number;
  username: string;
  email: string;
}

// 查询员工列表
export const getQueryEmployees = async (params: { page: number, size: number, searchQuery?: string }): Promise<EmployeeResponse> => {
  try {
    console.log(params,"params");
    
    const response = await ApiClient.get<EmployeeResponse>('adminUser/queryEmployees', params);
    console.log('API响应:', response); // 调试日志
    return response.data; // 返回完整的响应数据
  } catch (error) { 
    console.error("查询员工失败：", error);
    throw new Error('查询失败，请稍后再试');
  }
};

// 删除账号
export const deleteAccount = async (params: { id: number }): Promise<DeleteResponse> => {
  try {
    const response = await ApiClient.delete<DeleteResponse>('adminUser/deleteAccount', params);
    return response.data; // 返回数据
  } catch (error) {
    console.error("删除账号失败：", error);
    throw new Error('删除失败，请稍后再试');
  }
};

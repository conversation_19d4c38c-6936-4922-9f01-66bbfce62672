import React, { useState } from 'react';
import apiClient from '../api/api'; 

// 定义钱包交易 DTO 接口
interface WalletTransactionDTO {
  transactionId?: number;
  status?: string;
}

const SalaryManagement: React.FC = () => {
  // 加载状态
  const [isDistributing, setIsDistributing] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  // 消息状态
  const [message, setMessage] = useState<{ type: 'success' | 'error' | ''; text: string }>({
    type: '',
    text: ''
  });

  // 显示消息
  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 3000);
  };

  // 处理工资发放
  const handleDistributeSalary = async () => {
    setIsDistributing(true);
    try {
      const walletTransactionDTO: WalletTransactionDTO = {};

      const response = await apiClient.post<number>('walletTransaction/distributeSalary', walletTransactionDTO);
      
      if (response.code === 200) {
        showMessage('success', `发放成功！受影响记录数: ${response.data}`);
      } else {
        // 优先显示后端返回的具体错误信息
        const errorMessage = response.message || '发放失败';
        showMessage('error', errorMessage);
      }
    } catch (error: any) {
      console.error('发放工资失败:', error);
      
      // 优化错误信息提取逻辑，确保能获取到后端的具体错误信息
      let errorMessage = '发放失败，请重试';
      
      if (error.response && error.response.data) {
        // 尝试从 response.data.message 获取错误信息
        if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
        // 如果没有 message 字段，尝试直接使用 data 作为错误信息
        else if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        }
        // 如果 data 本身就是错误对象，尝试获取其中的错误信息
        else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      }
      // 如果上述都没有，尝试从 error.message 获取
      else if (error.message) {
        errorMessage = error.message;
      }
      // 最后尝试从 error.error 获取（根据您原来的代码）
      else if (error.error) {
        errorMessage = error.error;
      }
      
      showMessage('error', errorMessage);
    } finally {
      setIsDistributing(false);
    }
  };

  // 处理状态更新
  const handleUpdateStatus = async () => {
    setIsUpdatingStatus(true);
    try {
      const walletTransactionDTO: WalletTransactionDTO = {};

      const response = await apiClient.put<number>('walletTransaction/putStatus', walletTransactionDTO);
      
      if (response.code === 200) {
        showMessage('success', `更新成功！受影响记录数: ${response.data}`);
      } else {
        // 优先显示后端返回的具体错误信息
        const errorMessage = response.message || '更新失败';
        showMessage('error', errorMessage);
      }
    } catch (error: any) {
      console.error('更新状态失败:', error);
      
      // 优化错误信息提取逻辑，确保能获取到后端的具体错误信息
      let errorMessage = '更新失败，请重试';
      
      if (error.response && error.response.data) {
        // 尝试从 response.data.message 获取错误信息
        if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
        // 如果没有 message 字段，尝试直接使用 data 作为错误信息
        else if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        }
        // 如果 data 本身就是错误对象，尝试获取其中的错误信息
        else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      }
      // 如果上述都没有，尝试从 error.message 获取
      else if (error.message) {
        errorMessage = error.message;
      }
      // 最后尝试从 error.error 获取
      else if (error.error) {
        errorMessage = error.error;
      }
      
      showMessage('error', errorMessage);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // 添加提示显示函数
  const showWarningMessage = (message: string) => {
    showMessage('error', message);  // 使用 error 类型显示警告信息
  };

  // 点击发放工资时加上提示
  const handleDistributeSalaryWithWarning = async () => {
    // 显示警告提示
    showWarningMessage("请确保所有阿姨的余额已正确更新，点击发放工资前请再次检查！");

    // 然后执行原来的发放操作
    await handleDistributeSalary();
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">工资发放管理系统</h1>
      
      {/* 消息提示 */}
      {message.text && (
        <div className={`mb-6 p-4 rounded-lg ${
          message.type === 'success' 
            ? 'bg-green-100 border border-green-400 text-green-700' 
            : 'bg-red-100 border border-red-400 text-red-700'
        }`}>
          {message.text}
        </div>
      )}

      {/* 操作按钮区域 */}
      <div className="space-y-6">
        {/* 工资发放按钮 */}
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-xl font-semibold text-blue-800 mb-3">工资发放</h2>
          <p className="text-blue-600 text-sm mb-4">点击下方按钮执行工资发放操作</p>
          <button
            onClick={handleDistributeSalaryWithWarning} // 使用新的提示函数
            disabled={isDistributing || isUpdatingStatus}
            className={`w-full py-3 px-6 rounded-lg font-medium text-lg transition-all duration-200 ${
              isDistributing
                ? 'bg-blue-400 cursor-not-allowed transform scale-95'
                : isUpdatingStatus
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white hover:transform hover:scale-105 shadow-lg hover:shadow-xl'
            }`}
          >
            {isDistributing ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                发放中...
              </div>
            ) : '发放工资'}
          </button>
        </div>

        {/* 状态更新按钮 */}
        <div className="bg-green-50 p-6 rounded-lg border border-green-200">
          <h2 className="text-xl font-semibold text-green-800 mb-3">状态更新</h2>
          <p className="text-green-600 text-sm mb-4">点击下方按钮执行状态更新操作</p>
          <button
            onClick={handleUpdateStatus}
            disabled={isUpdatingStatus || isDistributing}
            className={`w-full py-3 px-6 rounded-lg font-medium text-lg transition-all duration-200 ${
              isUpdatingStatus
                ? 'bg-green-400 cursor-not-allowed transform scale-95'
                : isDistributing
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-green-600 hover:bg-green-700 text-white hover:transform hover:scale-105 shadow-lg hover:shadow-xl'
            }`}
          >
            {isUpdatingStatus ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                更新中...
              </div>
            ) : '更新状态'}
          </button>
        </div>
      </div>

      {/* 状态指示器 */}
      <div className="mt-8 flex justify-center space-x-4">
        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
          isDistributing ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-500'
        }`}>
          <div className={`w-2 h-2 rounded-full ${isDistributing ? 'bg-blue-500 animate-pulse' : 'bg-gray-300'}`}></div>
          <span>工资发放</span>
        </div>
        
        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
          isUpdatingStatus ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-500'
        }`}>
          <div className={`w-2 h-2 rounded-full ${isUpdatingStatus ? 'bg-green-500 animate-pulse' : 'bg-gray-300'}`}></div>
          <span>状态更新</span>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">操作说明</h3>
        <ul className="text-gray-600 space-y-1 text-sm">
          <li>• 点击"发放工资"按钮执行工资发放操作</li>
          <li>• 点击"更新状态"按钮执行状态更新操作</li>
          <li>• 操作执行期间按钮会显示加载状态</li>
          <li>• 同一时间只能执行一个操作</li>
          <li>• 操作结果会在页面顶部显示</li>
        </ul>
      </div>
    </div>
  );
};

export default SalaryManagement;
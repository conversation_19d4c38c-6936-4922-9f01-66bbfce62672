import React, { useEffect, useState } from 'react'
import { Card, Row, Col, DatePicker } from 'antd'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  getYearIncomeStats,
  getYearExpenseStats,
  getYearProfitStats,
  getMonthIncomeStats,
  getMonthExpenseStats,
  getMonthProfitStats,
  getDayIncomeStats,
  getDayExpenseStats,
  getDayProfitStats
} from '../api/stats'

const IncomeBarCharts: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(dayjs())
  const [chartsReady, setChartsReady] = useState(false)

  const [yearStats, setYearStats] = useState([0, 0, 0])
  const [monthStats, setMonthStats] = useState([0, 0, 0])
  const [dayStats, setDayStats] = useState([0, 0, 0])

  const fetchData = async () => {
    const year = selectedDate.year()
    const month = selectedDate.month() + 1
    const date = selectedDate.format('YYYY-MM-DD')

    const [yIncome, yExpense, yProfit] = await Promise.all([
      getYearIncomeStats(year),
      getYearExpenseStats(year),
      getYearProfitStats(year)
    ])
    setYearStats([yIncome.totalIncome, yExpense.totalExpense, yProfit.totalProfit])

    const [mIncome, mExpense, mProfit] = await Promise.all([
      getMonthIncomeStats(year, month),
      getMonthExpenseStats(year, month),
      getMonthProfitStats(year, month)
    ])
    setMonthStats([mIncome.totalIncome, mExpense.totalExpense, mProfit.totalProfit])

    const [dIncome, dExpense, dProfit] = await Promise.all([
      getDayIncomeStats(date),
      getDayExpenseStats(date),
      getDayProfitStats(date)
    ])
    setDayStats([dIncome.totalIncome, dExpense.totalExpense, dProfit.totalProfit])

    setChartsReady(true)
  }

  const renderChart = (id: string, data: number[], title: string) => {
    const chart = echarts.init(document.getElementById(id) as HTMLElement)
    chart.setOption({
      title: {
        text: title,
        left: 'center'
      },
      tooltip: {},
      grid: {
        left: 50, 
        right: 20,
        top: 50,
        bottom: 40
      },
      xAxis: {
        type: 'category',
        data: ['收入', '支出', '订单收益']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: data,
          type: 'bar',
          barWidth: '40%',
          itemStyle: {
            color: function (params: any) {
              const colors = ['#73C0DE', '#F2637B', '#91CC75']
              return colors[params.dataIndex]
            }
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c} 元'
          }
        }
      ]
    })
  }
  

  useEffect(() => {
    fetchData()
  }, [selectedDate])

  useEffect(() => {
    if (chartsReady) {
      renderChart('year-chart', yearStats, `${selectedDate.year()} 年统计`)
      renderChart('month-chart', monthStats, `${selectedDate.format('YYYY-MM')} 月统计`)
      renderChart('day-chart', dayStats, `${selectedDate.format('YYYY-MM-DD')} 日统计`)
    }
  }, [chartsReady, yearStats, monthStats, dayStats])

  return (
    <Card style={{ marginTop: 40 }}>
      <h3 style={{ marginBottom: 16 }}>📊 年 / 月 / 日 (订单)收入 - 支出 - 订单收益 统计</h3>
      <DatePicker
        value={selectedDate}
        onChange={v => setSelectedDate(v!)}
        picker="date"
        style={{ marginBottom: 24 }}
      />

      <Row gutter={[8, 24]}>
      <Col xs={24} md={8}><div id="year-chart" style={{ height: 300, paddingLeft: 24 }}></div></Col>
      <Col xs={24} md={8}><div id="month-chart" style={{ height: 300, paddingLeft: 24 }}></div></Col>
      <Col xs={24} md={8}><div id="day-chart" style={{ height: 300, paddingLeft: 24 }}></div></Col>
      </Row>
    </Card>
  )
}

export default IncomeBarCharts

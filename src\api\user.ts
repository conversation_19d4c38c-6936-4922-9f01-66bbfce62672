import ApiClient from "./api"

export interface User {
  userId: number
  phoneNumber: string
  nickname: string
  status: number
  registrationTime: string
  payPassword: string
  userSource: number
  updateTime: string
}

export interface UserDTO {
  page?: number
  size?: number
}

export interface UserResponse {
  records: User[]
  total: number
  size: number
  current: number
}

export function getUsers(data: UserDTO) {
  return ApiClient.post<any>(
    'user/getUser',
    data
  )
}
export function getUserById(data: { userId?: number, nickname?: string }) {
  // 根据传入的字段（userId 或 username）进行查询
  return ApiClient.post<{ code: number; message: string; data: User[] }>('user/getUserId', data)
}

export function putPassword(data: { userId:number; payPassword: string }) {
  return ApiClient.put<{ code: number; message: string; data: number }>('user/putPassword', data)
}

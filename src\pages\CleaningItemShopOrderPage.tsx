import React, { useEffect, useState } from 'react'
import { Card, Table, message, Button, Space, Tag, Modal, Badge, Tooltip } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { getAllCleaningItemShopOrder, getCleaningItemShopOrder, type CleaningItemShopOrder, modifyOrderStatus } from '../api/cleaningItemShopOrder'
import dayjs from 'dayjs'

const CleaningItemShopOrderPage: React.FC = () => {
  const [data, setData] = useState<CleaningItemShopOrder[]>([])
  const [loading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [searchOrderNumber, setSearchOrderNumber] = useState('')
  const [statusFilter, setStatusFilter] = useState<number | undefined>()
  const [unviewedCount, setUnviewedCount] = useState(0) // 未查看订单数量
  
  // 订单详情相关状态
  const [orderDetail, setOrderDetail] = useState<CleaningItemShopOrder | null>(null)
  const [modalVisible, setModalVisible] = useState(false)
  const [detailLoading, setDetailLoading] = useState(false)

  // 状态映射
  const statusMap: Record<number, { text: string; color: string }> = {
    0: { text: '待发货', color: 'orange' },
    1: { text: '已发货', color: 'blue' },
    2: { text: '已签收', color: 'green' },
    3: { text: '已退款', color: 'red' },
    4: { text: '待支付', color: 'purple' },
    5: { text: '已取消支付', color: 'gray' }
  }

  // 支付方式映射
  const paymentMethodMap: Record<number, string> = {
    1: '微信',
    2: '支付宝',
    3: '钱包'
  }

  // 地址截断显示函数
  const truncateAddress = (address: string, maxLength: number = 20) => {
    if (!address) return '-'
    if (address.length <= maxLength) return address
    return address.substring(0, maxLength) + '...'
  }

  // 查看订单详情
  const handleViewOrder = async (record: CleaningItemShopOrder) => {
    console.log('点击查看订单，当前view状态:', record.view)
    setDetailLoading(true)
    setModalVisible(true)
    
    // 先设置基础订单信息，避免null状态
    setOrderDetail(record)
    
    try {
      const res = await getCleaningItemShopOrder({ id: record.id })
      if (res.code === 200) {
        // 合并完整的订单详情
        setOrderDetail({
          ...record,
          ...res.data
        })
        
        // 如果订单之前未查看过，更新view状态为1
        if (record.view === 0) {
          console.log('订单未查看，准备更新view状态为1')
          try {
            const updateRes = await modifyOrderStatus({ 
              id: record.id, 
              view: 1,
              status: record.status  // 保持原有状态
            })
            console.log('更新view状态结果:', updateRes)
            
            if (updateRes.code === 200) {
              // 更新本地数据中的view状态
              setData(prevData => 
                prevData.map(item => 
                  item.id === record.id ? { ...item, view: 1 } : item
                )
              )
              // 减少未查看数量
              setUnviewedCount(prev => Math.max(0, prev - 1))
              
              // 通知父组件刷新未查看数量
              window.dispatchEvent(new CustomEvent('refreshUnviewedCount'))
            }
          } catch (updateError) {
            console.error('更新view状态失败:', updateError)
          }
        }
      } else {
        message.error('获取订单详情失败: ' + res.message)
        // 保持使用record的数据，不要覆盖
      }
    } catch (error) {
      message.error('获取订单详情失败')
      console.error('获取订单详情异常:', error)
      // 保持使用record的数据，不要覆盖
    } finally {
      setDetailLoading(false)
    }
  }

  // 关闭详情弹窗
  const handleCloseModal = () => {
    setModalVisible(false)
    setOrderDetail(null)
  }

  // 修改订单状态
  const handleModifyStatus = async (orderId: number, newStatus: number) => {
    try {
      const res = await modifyOrderStatus({ id: orderId, status: newStatus })
      if (res.code === 200) {
        message.success('订单状态修改成功')
        if (orderDetail) {
          setOrderDetail({ ...orderDetail, status: newStatus })
        }
        fetchOrders()
      } else {
        message.error('修改失败: ' + res.message)
      }
    } catch (error) {
      message.error('修改失败: ' + (error as any).message)
    }
  }

  const columns: ColumnsType<CleaningItemShopOrder> = [
    { title: 'ID', dataIndex: 'id', width: 80 },
    {title:'订单编号', dataIndex: 'orderNumber', width: 150 },
    { title: '阿姨姓名', dataIndex: 'caregiverName', width: 100 },
    { title: '商品名称', dataIndex: 'itemName', width: 100 },
    { title: '单价', dataIndex: 'unitPrice', width: 100 },
    { title: '数量', dataIndex: 'quantity', width: 80 },
    { title: '进货价', dataIndex: 'purchasePrice', width: 100 },
    { title: '利润', dataIndex: 'profit', width: 100, 
      render: (_: any, record: CleaningItemShopOrder) => {
        const profit = (record.unitPrice * record.quantity) - (record.purchasePrice * record.quantity)
        return `¥${profit?.toFixed(2) || '0.00'}`
      }
    },
    { 
      title: '总价格', 
      dataIndex: 'totalPrice', 
      width: 100,
      render: (price: number) => `¥${price?.toFixed(2) || '0.00'}`
    },
    { 
      title: '地址', 
      dataIndex: 'fullAddress', 
      width: 150,
      render: (address: string) => (
        address ? (
          <Tooltip title={address} placement="topLeft">
            <span style={{ cursor: 'pointer' }}>
              {truncateAddress(address, 15)}
            </span>
          </Tooltip>
        ) : '-'
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status: number) => (
        <Tag color={statusMap[status]?.color || 'default'}>
          {statusMap[status]?.text || '未知'}
        </Tag>
      )
    },
    {
      title: '支付方式',
      dataIndex: 'paymentMethod',
      width: 100,
      render: (method: number) => paymentMethodMap[method] || '-'
    },
    { title: '支付订单号', dataIndex: 'payOrderNumber', width: 150 },
    {
      title: '支付时间',
      dataIndex: 'payTime',
      width: 150,
      render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    { title: '备注', dataIndex: 'remark', width: 150 },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      render: (_, record) => (
        <Badge dot={record.view === 0} offset={[-5, 5]}>
          <Button type="link" onClick={() => handleViewOrder(record)}>
            查看
          </Button>
        </Badge>
      )
    }
  ]

  // 获取订单列表
  const fetchOrders = async () => {
    setLoading(true)
    try {
      const params = {
        page,
        size: pageSize,
        ...(searchOrderNumber && { orderNumber: searchOrderNumber }),
        ...(statusFilter !== undefined && { status: statusFilter })
      }
      
      const res = await getAllCleaningItemShopOrder(params)
      
      if (res.code === 200) {
        const pageData = res.data
        setData(pageData.records || [])
        setTotal(pageData.total || 0)
        
        // 计算未查看订单数量
        const unviewed = (pageData.records || []).filter(item => item.view === 0).length
        setUnviewedCount(unviewed)
      } else {
        message.error('获取订单失败: ' + res.message)
      }
    } catch (error) {
      message.error('请求失败: ' + (error as any).message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrders()
  }, [page, pageSize, searchOrderNumber, statusFilter])

  // 订单详情弹窗内容
  const renderOrderDetail = () => {
    if (!orderDetail) return null

    return (
      <div>
        <p><strong>订单ID:</strong> {orderDetail.id || '-'}</p>
        <p><strong>订单编号:</strong> {orderDetail.orderNumber || '-'}</p>
        <p><strong>阿姨姓名:</strong> {orderDetail.caregiverName || '-'}</p>
        <p><strong>商品名称:</strong> {orderDetail.itemName || '-'}</p>
        <p><strong>单价:</strong> ¥{orderDetail.unitPrice?.toFixed(2) || '0.00'}</p>
        <p><strong>数量:</strong> {orderDetail.quantity || '-'}</p>
        <p><strong>进货价:</strong> ¥{orderDetail.purchasePrice?.toFixed(2) || '0.00'}</p>
        <p><strong>总价格:</strong> ¥{orderDetail.totalPrice?.toFixed(2) || '0.00'}</p>
        <p><strong>地址:</strong> {orderDetail.fullAddress || '-'}</p>
        <p><strong>状态:</strong> 
          <Tag color={statusMap[orderDetail.status]?.color || 'default'} style={{ marginLeft: 8 }}>
            {statusMap[orderDetail.status]?.text || '未知'}
          </Tag>
        </p>
        <p><strong>支付方式:</strong> {paymentMethodMap[orderDetail.paymentMethod] || '-'}</p>
        <p><strong>支付订单号:</strong> {orderDetail.payOrderNumber || '-'}</p>
        <p><strong>支付时间:</strong> {orderDetail.payTime ? dayjs(orderDetail.payTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</p>
        <p><strong>备注:</strong> {orderDetail.remark || '-'}</p>
        <p><strong>创建时间:</strong> {orderDetail.createTime ? dayjs(orderDetail.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</p>
        <p><strong>更新时间:</strong> {orderDetail.updateTime ? dayjs(orderDetail.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</p>
        
        {/* 状态操作按钮 */}
        <div style={{ marginTop: 20, padding: '16px 0', borderTop: '1px solid #f0f0f0' }}>
          <p><strong>订单状态操作:</strong></p>
          <Space>
            {orderDetail.status === 0 && (
              <Button 
                type="primary" 
                onClick={() => handleModifyStatus(orderDetail.id, 1)}
              >
                标记为已发货
              </Button>
            )}
            {orderDetail.status === 1 && (
              <Button 
                type="primary" 
                onClick={() => handleModifyStatus(orderDetail.id, 2)}
              >
                标记为已签收
              </Button>
            )}
            {orderDetail.status === 4 && (
              <Button 
                type="primary" 
                onClick={() => handleModifyStatus(orderDetail.id, 0)}
              >
                标记为待发货
              </Button>
            )}
            {(orderDetail.status === 0 || orderDetail.status === 4) && (
              <Button 
                danger 
                onClick={() => handleModifyStatus(orderDetail.id, 3)}
              >
                退款
              </Button>
            )}
            {orderDetail.status === 4 && (
              <Button 
                onClick={() => handleModifyStatus(orderDetail.id, 5)}
                style={{ backgroundColor: '#666', color: 'white' }}
              >
                取消支付
              </Button>
            )}
          </Space>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: 24 }}>
      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>清洁用品订单管理</span>
            {unviewedCount > 0 && (
              <Badge 
                count={unviewedCount} 
                style={{ marginLeft: 16 }}
                title={`有 ${unviewedCount} 个新订单待查看`}
              />
            )}
          </div>
        }
      >
        {unviewedCount > 0 && (
          <div style={{ 
            marginBottom: 16, 
            padding: '8px 16px', 
            backgroundColor: '#fff7e6', 
            border: '1px solid #ffd591',
            borderRadius: '6px',
            color: '#d46b08'
          }}>
            <span>📢 您有 {unviewedCount} 个新订单待查看，请及时处理！</span>
          </div>
        )}
        
        <Table 
          dataSource={data}
          rowKey="id"
          loading={loading}
          columns={columns}
          pagination={{
            current: page,
            pageSize,
            total,
            onChange: (p, ps) => {
              setPage(p)
              setPageSize(ps || 10)
            },
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 订单详情弹窗 */}
      <Modal
        title="订单详情"
        visible={modalVisible}
        onCancel={handleCloseModal}
        footer={[
          <Button key="close" onClick={handleCloseModal}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {detailLoading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>加载中...</div>
        ) : (
          renderOrderDetail()
        )}
      </Modal>
    </div>
  )
}

export default CleaningItemShopOrderPage
import React, { useState } from 'react'
import { Form, Input, InputNumber, Button, DatePicker, Select, message, Switch } from 'antd'
import apiClient from '../api/api'

const { RangePicker } = DatePicker
const { TextArea } = Input

const CreateCouponForm: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()
  const [discountType, setDiscountType] = useState<number | undefined>()

  // 正确使用 message API
  const [messageApi, contextHolder] = message.useMessage()

  const onFinish = async (values: any) => {
    console.log('表单已提交：', values)
    setLoading(true)
    try {
      const response = await apiClient.post('coupon/addCoupon', {
        title: values.title,
        description: values.description,
        discountType: values.discountType,  
        couponType: values.couponType,
        distributionMethod: values.distributionMethod,
        fullAmount: values.fullAmount,
        discountValue: values.discountValue,
        maxDiscount: values.maxDiscount,
        validDay: values.validDay,
        limitCount: values.limitCount,
        validStartTime: values.validTime[0].format('YYYY-MM-DD HH:mm:ss'),
        validEndTime: values.validTime[1].format('YYYY-MM-DD HH:mm:ss'),
        collectStartTime: values.collectTime[0].format('YYYY-MM-DD HH:mm:ss'),
        collectEndTime: values.collectTime[1].format('YYYY-MM-DD HH:mm:ss'),
        quantity: values.quantity,
        remainingQuantity: values.remainingQuantity || values.quantity,
      })

      console.log('接口响应：', response)

      // 正确使用 messageApi
      messageApi.success({
        content: '优惠券创建成功！',
        duration: 1.5,
        style: { zIndex: 9999 }
      })

    } catch (error) {
      console.error('提交出错：', error)
      messageApi.error('创建失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理折扣类型变化
  const handleDiscountTypeChange = (value: number) => {
    setDiscountType(value)
    // 当切换折扣类型时，清空相关字段的值
    if (value === 1) {
      // 固定金额：清空最大折扣
      form.setFieldsValue({ maxDiscount: undefined })
    } else if (value === 2) {
      // 百分比：清空折扣值
      form.setFieldsValue({ discountValue: undefined })
    }
  }

  return (
    <>
      {/* message contextHolder 必须加在页面内才能生效 */}
      {contextHolder}

      <Form
        layout="vertical"
        form={form}
        onFinish={onFinish}
      >
        <Form.Item label="优惠券标题" name="title" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item label="描述" name="description">
          <TextArea rows={2} />
        </Form.Item>
        <Form.Item label="折扣类型" name="discountType" rules={[{ required: true }]}>
          <Select onChange={handleDiscountTypeChange}>
            <Select.Option value={1}>固定金额</Select.Option>
            <Select.Option value={2}>百分比</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="优惠券类型" name="couponType" rules={[{ required: true }]}>
          <Select placeholder="请选择优惠券类型">
            <Select.Option value={0}>通用</Select.Option>
            <Select.Option value={1}>普通保洁</Select.Option>
            <Select.Option value={2}>开荒保洁</Select.Option>
            <Select.Option value={3}>玻璃清洗</Select.Option>
            <Select.Option value={4}>机器拆洗</Select.Option>
            <Select.Option value={5}>企业保洁</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="发放方式" name="distributionMethod" rules={[{ required: true }]}>
          <Select>
            <Select.Option value={1}>下单赠送</Select.Option>
            <Select.Option value={2}>注册赠送</Select.Option>
            <Select.Option value={3}>充值赠送</Select.Option>
            <Select.Option value={4}>活动领取</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="满多少" name="fullAmount" rules={[{ required: true }]}>
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>
        
        {/* 根据折扣类型控制字段状态 */}
        <Form.Item 
          label="减多少/折扣值" 
          name="discountValue" 
          rules={[{ required: true, message: '请输入折扣值' }]}
        >
          <InputNumber 
            min={0} 
            style={{ width: '100%' }} 
            placeholder={discountType === 2 ? '请输入减免金额' : '请输入减免金额'}
          />
        </Form.Item>
        
        <Form.Item 
          label="最大折扣" 
          name="maxDiscount" 
          rules={discountType === 1 ? [] : [{ required: true }]} // 固定金额时不必填
        >
          <InputNumber 
            min={0} 
            style={{ width: '100%' }} 
            disabled={discountType === 1} // 固定金额时禁用
            placeholder={discountType === 1 ? '固定金额模式下此字段不可用' : '请输入最大折扣金额'}
          />
        </Form.Item>
        
        <Form.Item label="有效天数" name="validDay" rules={[{ required: true }]}>
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item label="限领次数" name="limitCount" rules={[{ required: true }]}>
          <InputNumber min={1} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item label="使用时间区间" name="validTime" rules={[{ required: true }]}>
          <RangePicker showTime style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item label="领取时间区间" name="collectTime" rules={[{ required: true }]}>
          <RangePicker showTime style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item label="发行数量" name="quantity" rules={[{ required: true }]}>
          <InputNumber min={1} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item label="剩余可用数量" name="remainingQuantity">
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>提交</Button>
        </Form.Item>
      </Form>
    </>
  )
}

export default CreateCouponForm
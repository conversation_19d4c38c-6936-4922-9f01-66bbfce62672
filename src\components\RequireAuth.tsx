// src/components/RequireAuth.tsx
import React, { useEffect } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { App as AntdApp } from 'antd'

interface Props {
  children: React.ReactNode
}

const RequireAuth: React.FC<Props> = ({ children }) => {
  const token = localStorage.getItem('token')
  const location = useLocation()
  const { message } = AntdApp.useApp()

  useEffect(() => {
    if (!token) {
      message.warning('请先登录后再访问')
    }
  }, [token])

  if (!token) {
    return <Navigate to="/login" replace state={{ from: location }} />
  }

  return <>{children}</>
}

export default RequireAuth

import React, { useEffect, useState } from 'react'
import {
  Table, Button, Modal, Form, InputNumber,
  Space, message, Card, Tooltip, Popconfirm
} from 'antd'
import { EditOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons'
import {
  getGiftCouponProbability,
  updateGiftCouponProbability,
  addGiftCouponProbability,
  deleteGiftCouponProbability,
  type GiftCouponProbability,
  type GiftCouponProbabilityDTO
} from '../api/giftCouponProbability'

const GiftCouponProbabilityPage: React.FC = () => {
  const [data, setData] = useState<any>([])
  const [loading, setLoading] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<GiftCouponProbability | null>(null)
  const [form] = Form.useForm()
  const [messageApi, contextHolder] = message.useMessage()

  // 查询列表
  const fetchData = async () => {
    setLoading(true)
    try {
      console.log('开始获取数据...')
      const res = await getGiftCouponProbability()
      console.log('API 响应:', res)
      
      if (res.code === 200) {
        console.log('数据:', res.data)
        const dataArray = res.data.data || res.data || []
        setData(dataArray)
        console.log('设置的数据:', dataArray)
      } else {
        console.error('API 错误:', res.message)
        messageApi.error(res.message || '查询失败')
      }
    } catch (error) {
      console.error('请求异常:', error)
      messageApi.error('查询失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  // 打开编辑弹窗
  const openEditModal = (item: GiftCouponProbability) => {
    setEditingItem(item)
    setIsModalOpen(true)
    form.setFieldsValue({
      releaseProbability: item.releaseProbability
    })
  }

  // 打开新增弹窗
  const openAddModal = () => {
    setEditingItem(null)
    setIsModalOpen(true)
    form.resetFields()
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      
      if (editingItem) {
        // 更新
        const updateData: GiftCouponProbabilityDTO = {
          id: editingItem.id,
          releaseProbability: values.releaseProbability
        }
        const res = await updateGiftCouponProbability(updateData)
        if (res.code === 200) {
          messageApi.success('更新成功')
          setIsModalOpen(false)
          fetchData()
        } else {
          messageApi.error(res.message || '更新失败')
        }
      } else {
        // 新增
        const res = await addGiftCouponProbability(values)
        if (res.code === 200) {
          messageApi.success('添加成功')
          setIsModalOpen(false)
          fetchData()
        } else {
          messageApi.error(res.message || '添加失败')
        }
      }
    } catch (error) {
      messageApi.error('操作失败')
    }
  }

  // 删除
  const handleDelete = async (item: GiftCouponProbability) => {
    try {
      const res = await deleteGiftCouponProbability({ id: item.id })
      if (res.code === 200) {
        messageApi.success('删除成功')
        fetchData()
      } else {
        messageApi.error(res.message || '删除失败')
      }
    } catch (error) {
      messageApi.error('删除失败')
    }
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '优惠券ID',
      dataIndex: 'couponId',
      key: 'couponId',
      width: 100
    },
    {
      title: '优惠券标题',
      dataIndex: 'couponTitle',
      key: 'couponTitle',
      width:150,
      ellipsis: {
        showTitle: false
      },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text || '-'}
        </Tooltip>
      )
    },
    {
      title: '优惠券描述',
      dataIndex: 'couponDescription',
      key: 'couponDescription',
      width:200,
      ellipsis: {
        showTitle: false
      },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text || '-'}
        </Tooltip>
      )
    },
    {
      title: '发放概率 (%)',
      dataIndex: 'releaseProbability',
      key: 'releaseProbability',
      width: 120,
      render: (value: number) => `${value}%`
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: GiftCouponProbability) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除该记录？"
            onConfirm={() => handleDelete(record)}
            okText="确认"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <>
      {contextHolder}
      
      <Card 
        title="🎁 优惠券发放概率管理"
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={openAddModal}
          >
            新增概率
          </Button>
        }
      >
        <Table
          rowKey="id"
          dataSource={data}
          columns={columns}
          loading={loading}
          scroll={{ x: 800 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 编辑弹窗 */}
      <Modal
        title={editingItem ? '编辑发放概率' : '新增发放概率'}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleSubmit}
        destroyOnHidden
      >
        <Form form={form} layout="vertical">
          {!editingItem && (
            <Form.Item
              name="couponId"
              label="优惠券ID"
              rules={[
                { required: true, message: '请输入优惠券ID' },
                { type: 'number', min: 1, message: '请输入有效的优惠券ID' }
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                placeholder="请输入优惠券ID"
              />
            </Form.Item>
          )}
          <Form.Item
            name="releaseProbability"
            label="发放概率 (%)"
            rules={[
              { required: true, message: '请输入发放概率' },
              { type: 'number', min: 0, max: 100, message: '概率必须在0-100之间' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              max={100}
              precision={2}
              placeholder="请输入0-100之间的数值"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default GiftCouponProbabilityPage
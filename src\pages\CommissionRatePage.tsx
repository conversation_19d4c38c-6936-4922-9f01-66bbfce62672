import React, { useEffect, useState } from 'react'
import { Table, Card, Button, Modal, Form, Input, Popconfirm, Space,App } from 'antd'
import apiClient from '../api/api'

interface SysConfig {
  id?: number
  configKey: string
  configValue: string
  description: string
  createTime?: string
  updateTime?: string
}

const CommissionRatePage: React.FC = () => {
  const { message } = App.useApp()
  const [data, setData] = useState<SysConfig[]>([])
  const [loading, setLoading] = useState(false)
  const [editingItem, setEditingItem] = useState<SysConfig | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [form] = Form.useForm()
  
  // 新增提交加载状态
  const [submitLoading, setSubmitLoading] = useState(false)

  // 分页参数
  const [page, setPage] = useState<number>(1)
  const [size, setSize] = useState<number>(10)
  const [total, setTotal] = useState<number>(0)

  // 查询数据
  const fetchData = async (pageNum = page, pageSize = size) => {
    setLoading(true)
    try {
      const res = await apiClient.post<any>('sysConfig/getSysConfig', {
        page: pageNum,
        size: pageSize,
      })
      const records = res.data?.records || []
      setData(records)
      setTotal(res.data?.total || 0)
    } catch (e: any) {
      message.error('加载失败：' + (e.message || '未知错误'))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [page, size])

  // 打开编辑弹窗
  const handleEdit = (item: SysConfig) => {
    setEditingItem(item)
    form.setFieldsValue(item)
    setIsModalOpen(true)
  }

  // 打开新增弹窗
  const handleAdd = () => {
    setEditingItem(null)
    form.resetFields()
    setIsModalOpen(true)
  }

  // 提交（新增/编辑）
  const handleSubmit = async () => {
    // 设置提交加载状态
    setSubmitLoading(true)
    
    try {
      const values = await form.validateFields()
      const url = editingItem?.id ? 'sysConfig/putSysConfig' : 'sysConfig/postSysConfig'
      const payload = editingItem?.id ? { ...editingItem, ...values } : values

      console.log('开始提交:', editingItem?.id ? '修改' : '新增', payload)

      let res = null
      if(url == 'sysConfig/putSysConfig'){
         res = await apiClient.put(url, payload)
      } else {
         res = await apiClient.post(url, payload)
      }

      console.log('提交结果:', res)

      if (res?.code === 200) {
        // 确保成功提示显示
        message.destroy() // 清除之前的消息
        message.success(editingItem?.id ? '修改成功！' : '新增成功！')
        
        console.log('显示成功提示:', editingItem?.id ? '修改成功' : '新增成功')
        
        // 延迟关闭弹窗，让用户看到成功提示
        setTimeout(() => {
          setIsModalOpen(false)
          setSubmitLoading(false)
        }, 500)
        
        // 刷新数据
        fetchData(1, size)
        setPage(1)
        
      } else {
        // 优化错误信息提取逻辑
        let errorMessage = editingItem?.id ? '修改失败' : '新增失败'
        
        if (res?.message) {
          errorMessage = res.message
        } else if (res?.data?.message) {
          errorMessage = res.data.message
        } else if (typeof res?.data === 'string') {
          errorMessage = res.data
        }
        
        console.log('显示错误提示:', errorMessage)
        message.destroy()
        message.error({
          content: errorMessage,
          duration: 5,
          style: {
            marginTop: '20vh',
          }
        })
        setSubmitLoading(false)
      }
    } catch (e: any) {
      console.error('提交出错:', e)
      
      // 优化错误信息提取逻辑
      let errorMessage = '提交出错'
      
      if (e.response && e.response.data) {
        if (e.response.data.message) {
          errorMessage = e.response.data.message
        } else if (typeof e.response.data === 'string') {
          errorMessage = e.response.data
        } else if (e.response.data.error) {
          errorMessage = e.response.data.error
        }
      } else if (e.message) {
        errorMessage = e.message
      } else if (e.error) {
        errorMessage = e.error
      }
      
      console.log('显示异常提示:', errorMessage)
      message.destroy()
      message.error({
        content: errorMessage,
        duration: 5,
        style: {
          marginTop: '20vh',
        }
      })
      setSubmitLoading(false)
    }
  }

  // 关闭弹窗时重置状态
  const handleCloseModal = () => {
    if (!submitLoading) { // 只有不在提交状态时才允许关闭
      setIsModalOpen(false)
      setSubmitLoading(false)
      form.resetFields()
    }
  }

  // 表格列定义
  const columns = [
    { title: '配置名称', dataIndex: 'configKey', key: 'configKey' },
    { title: '比例值', dataIndex: 'configValue', key: 'configValue' },
    { title: '描述', dataIndex: 'description', key: 'description' },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
    { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime' },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: SysConfig) => (
        <Space>
          <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
        </Space>
      ),
    },
  ]

  return (
    <Card
      title="平台抽成比例配置"
      extra={<Button type="primary" onClick={handleAdd}>➕ 新增配置</Button>}
    >
      <Table
        rowKey="id"
        dataSource={data}
        columns={columns}
        loading={loading}
        pagination={{
          current: page,
          pageSize: size,
          total,
          showTotal: t => `共 ${t} 条`,
          onChange: (p, s) => {
            setPage(p)
            setSize(s)
          },
          showSizeChanger: true,
        }}
      />

      <Modal
        title={editingItem?.id ? '编辑抽成配置' : '新增抽成配置'}
        open={isModalOpen}
        onCancel={handleCloseModal}
        onOk={handleSubmit}
        destroyOnClose
        confirmLoading={submitLoading} // 添加确认按钮加载状态
        // 自定义按钮文本和加载状态
        okText={submitLoading ? (editingItem?.id ? '修改中...' : '新增中...') : '确定'}
        cancelButtonProps={{
          disabled: submitLoading // 提交中禁用取消按钮
        }}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="configKey" label="配置名称" rules={[{ required: true, message: '请输入配置名称' }]}>
            <Input placeholder="请输入配置名称" disabled={submitLoading} />
          </Form.Item>
          <Form.Item 
            name="configValue" 
            label="比例值" 
            rules={[
              { required: true, message: '请输入比例值' },
              { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字' }
            ]}
          >
            <Input placeholder="请输入比例值（如：0.05 表示5%）" disabled={submitLoading} />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <Input.TextArea 
              rows={3} 
              placeholder="请输入描述信息" 
              disabled={submitLoading}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}

export default CommissionRatePage
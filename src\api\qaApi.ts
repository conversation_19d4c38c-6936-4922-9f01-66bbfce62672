import ApiClient from "./api"

export interface QAItem {
  question: string;
  answer: string;
}

export interface AddQAParams {
  question: string;
  answer: string;
  categoryId: number;
}

export interface AddQAResponse {
  code: number;
  message: string;
  data?: any;
}

export interface QAListResponse {
  code: number;
  message: string;
  data: {
    records: QAItem[];
    total: number;
    current: number;
    size: number;
  };
}

// 分类单项
export interface Category {
  categoryId: number;
  categoryName: string;

}

// 分类接口返回
export interface CategoryResponse {
  code: number;
  message: string;
  data: Category[];
}

export function addQA(params: AddQAParams) {
  return ApiClient.post<AddQAResponse>('/chatbot/addQA', params);
}

export function importQA(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return ApiClient.post<AddQAResponse>('/chatbot/importQA', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export function fetchCategories() {
  return ApiClient.get<CategoryResponse>("/chatbotQuestionCategory");
}

export function fetchQAList(currentPage: number, pageSize: number, categoryId?: string) {
  return ApiClient.get<QAListResponse>("/chatbot/QAList", {
     currentPage, pageSize, categoryId 
  });
}

import React, { useEffect, useState } from 'react'
import { Table, Tag, Button, message, Space } from 'antd'
import ApiClient from '../api/api'
import { useNavigate } from 'react-router-dom'

const CaregiverAuditListPage: React.FC = () => {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(false)
  const [page, setPage] = useState(1)          // 当前页
  const [pageSize] = useState(10)              // 每页数量
  const [total, setTotal] = useState(0)        // 总条数
  const navigate = useNavigate()

  // 获取数据
  const fetchData = async () => {
    setLoading(true)
    try {
      const res = await ApiClient.post<any>('caregiver/getCaregiver', {
        page: page,
        size: pageSize
      })
      const result = res.data
      setData(result.records || [])
      setTotal(result.total || 0)
    } catch (e) {
      message.error('加载失败')
    } finally {
      setLoading(false)
    }
  }

  // 监听页码变化
  useEffect(() => {
    fetchData()
  }, [page])

  // 状态显示
  const renderStatus = (val: number | string) => (
    val == 1 ? <Tag color="green">已通过</Tag> : <Tag color="red">未通过</Tag>
  )

  const renderAuditResult = (record: any) => {
    return record.idCardVerification == 1 && record.healthCertificateVerification == 1
      ? <Tag color="green">审核通过</Tag>
      : <Tag color="red">未通过</Tag>
  }

  return (
    <Table
      dataSource={data}
      rowKey="caregiverId"
      loading={loading}
      pagination={{
        current: page,               // 当前页码
        pageSize: pageSize,         // 每页条数
        total: total,               // 总记录数
        onChange: (p) => setPage(p) // 切页事件
      }}
    >
      <Table.Column title="ID" dataIndex="caregiverId" />
      <Table.Column title="姓名" dataIndex="name" />
      <Table.Column title="手机号" dataIndex="phoneNumber" />
      <Table.Column
        title="身份证状态"
        dataIndex="idCardVerification"
        render={renderStatus}
      />
      <Table.Column
        title="健康证状态"
        dataIndex="healthCertificateVerification"
        render={renderStatus}
      />
      <Table.Column
        title="审核状态"
        render={(_, record) => renderAuditResult(record)}
      />
      <Table.Column
        title="操作"
        render={(_, record) => (
          <Space>
            <Button type="link" onClick={() => navigate(`/caregiver-audit/${record.caregiverId}`)}>
              查看审核
            </Button>
          </Space>
        )}
      />
    </Table>
  )
}

export default CaregiverAuditListPage

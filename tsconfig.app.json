{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    // "strict": true,
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,

    "noImplicitAny": false,           // 允许隐式 any 类型
    "noUnusedLocals": false,          // 不检查未使用的局部变量
    "noUnusedParameters": false,      // 不检查未使用的参数
    "strict": false,                  // 关闭严格模式（包含多个检查）
    // "skipLibCheck": true              // 跳过库文件的类型检查
  },
  "include": ["src"]
}

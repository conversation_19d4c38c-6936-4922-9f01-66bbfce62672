import ApiClient from "./api"

// 用户订单类型
export interface UserOrder {
  orderId: number
  orderNumber: string
  userId: number
  userName: string
  caregiversId: number
  caregiverName: string
  addressId: number
  status: number
  serviceTypeId: number
  appointmentTime: string
  orderCompletionTime: string
  totalPrice: number
  serviceVerificationImages: string[]
  view?: number  // 查看状态：0-未查看，1-已查看
}

// 订单查询参数
export interface OrderDTO {
  page?: number
  size?: number
  orderId?: number
  orderNumber?: string
  userName?: string
  caregiverName?: string
}

// 订单响应格式
export interface OrderResponse {
  records: UserOrder[]
  total: number
  size: number
  current: number
  pages: number
}

// 获取用户订单列表（分页）
export function getUserOrders(params: OrderDTO) {
  return ApiClient.post<OrderResponse>('order/getOrder', params)
}

// 根据条件查询订单
export function getUserOrderById(params: OrderDTO) {
  return ApiClient.post<UserOrder[]>('order/getOrderId', params)
}

// 获取订单详情（包括图片）
export function getUserOrderDetail(params: { orderId: number }) {
  return ApiClient.post<UserOrder[]>('order/orderId', params)
}

// 更新订单查看状态
export function updateOrderViewStatus(params: { orderId: number }) {
  return ApiClient.put<number>('order/updateView', params)
}

import React, { useState } from 'react'
import { Button, Card, Space, Typography, Divider } from 'antd'
import {
  getCaregiverTotal,
  getUserTotal,
  getTotalIncomeStats,
  getTotalExpenseStats,
  getTotalProfitStats,
  getOrderTypeCounts,
  getToolIncome,
  getTotalPlatformProfit,
  getOtherCompanyOrderAmount,
} from '../api/stats'

const { Text, Paragraph } = Typography

const ApiTest: React.FC = () => {
  const [results, setResults] = useState<Record<string, any>>({})
  const [loading, setLoading] = useState<Record<string, boolean>>({})

  const testApi = async (apiName: string, apiFunction: () => Promise<any>) => {
    setLoading(prev => ({ ...prev, [apiName]: true }))
    try {
      console.log(`🔄 测试 ${apiName}...`)
      const result = await apiFunction()
      console.log(`✅ ${apiName} 成功:`, result)
      setResults(prev => ({ ...prev, [apiName]: { success: true, data: result } }))
    } catch (error) {
      console.error(`❌ ${apiName} 失败:`, error)
      setResults(prev => ({ ...prev, [apiName]: { success: false, error: error.message } }))
    } finally {
      setLoading(prev => ({ ...prev, [apiName]: false }))
    }
  }

  const testConnection = async () => {
    try {
      console.log('🔄 测试网络连接...')
      const response = await fetch('/api/caregiver/total', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'token': `Bearer ${localStorage.getItem('token')}`
        }
      })

      console.log('🌐 网络响应状态:', response.status)
      console.log('🌐 网络响应头:', Object.fromEntries(response.headers.entries()))

      if (response.ok) {
        const data = await response.json()
        console.log('🌐 网络响应数据:', data)
        setResults(prev => ({ ...prev, '网络连接': { success: true, data: `状态码: ${response.status}, 数据: ${JSON.stringify(data)}` } }))
      } else {
        const errorText = await response.text()
        console.log('🌐 网络错误响应:', errorText)
        setResults(prev => ({ ...prev, '网络连接': { success: false, error: `状态码: ${response.status}, 错误: ${errorText}` } }))
      }
    } catch (error) {
      console.error('❌ 网络连接失败:', error)
      setResults(prev => ({ ...prev, '网络连接': { success: false, error: error.message } }))
    }
  }

  const testAllApis = async () => {
    // 先测试网络连接
    await testConnection()

    const apis = [
      { name: '阿姨总数', func: getCaregiverTotal },
      { name: '用户总数', func: getUserTotal },
      { name: '总收入统计', func: getTotalIncomeStats },
      { name: '总支出统计', func: getTotalExpenseStats },
      { name: '总利润统计', func: getTotalProfitStats },
      { name: '订单类型统计', func: getOrderTypeCounts },
      { name: '工具收入', func: getToolIncome },
      { name: '平台总利润', func: getTotalPlatformProfit },
      { name: '其他平台阿姨收入', func: getOtherCompanyOrderAmount },
    ]

    for (const api of apis) {
      await testApi(api.name, api.func)
      // 添加小延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  const renderResult = (apiName: string) => {
    const result = results[apiName]
    if (!result) return null

    return (
      <Card size="small" style={{ marginBottom: 8 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text strong>{apiName}</Text>
          <Text type={result.success ? 'success' : 'danger'}>
            {result.success ? '✅ 成功' : '❌ 失败'}
          </Text>
        </div>
        {result.success ? (
          <Paragraph code style={{ marginTop: 8, marginBottom: 0 }}>
            {JSON.stringify(result.data, null, 2)}
          </Paragraph>
        ) : (
          <Text type="danger" style={{ fontSize: '12px' }}>
            错误: {result.error}
          </Text>
        )}
      </Card>
    )
  }

  return (
    <Card title="API 测试工具" style={{ margin: 16 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Button 
            type="primary" 
            onClick={testAllApis}
            loading={Object.values(loading).some(Boolean)}
          >
            测试所有API
          </Button>
          <Button 
            style={{ marginLeft: 8 }}
            onClick={() => {
              setResults({})
              console.clear()
            }}
          >
            清空结果
          </Button>
        </div>

        <Divider />

        <div>
          <Text strong>当前Token状态:</Text>
          <Paragraph code>
            {localStorage.getItem('token') ? '✅ 已设置' : '❌ 未设置'}
          </Paragraph>
        </div>

        <Divider />

        <div>
          {Object.keys(results).length > 0 && (
            <>
              <Text strong>测试结果:</Text>
              <div style={{ marginTop: 8 }}>
                {Object.keys(results).map(apiName => renderResult(apiName))}
              </div>
            </>
          )}
        </div>
      </Space>
    </Card>
  )
}

export default ApiTest

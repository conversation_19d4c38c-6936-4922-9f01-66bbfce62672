import React, { useEffect, useState } from 'react';
import { Table, Card, message, Input, Button, Space, Modal } from 'antd';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import { getCaregivers, getCaregiverById, getCaregiverPermission, addCaregiverPermission, deleteCaregiverPermission } from '../api/caregiver';
import type { Caregiver } from '../api/caregiver';

const CaregiverList: React.FC = () => {
  const [data, setData] = useState<any>(); // 当前展示数据
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [loading, setLoading] = useState(false);

  const [caregiverId, setCaregiverId] = useState<string>(''); // 输入框内容
  const [name, setName] = useState<string>(''); // 输入的姓名
  const [isSearchMode, setIsSearchMode] = useState(false); // 是否为搜索状态
  const [permissionData, setPermissionData] = useState<any>([]); // 用来存放查询权限的数据
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false); // 控制权限弹窗的显示
  const [currentCaregiverId, setCurrentCaregiverId] = useState<number>(0); // 当前操作的阿姨ID

  // 服务类型ID到名称的映射
  const serviceTypeMap = {
    1: '普通保洁',
    2: '开荒保洁',
    3: '玻璃清洗',
    4: '机器清洗',
    5: '企业保洁',
  };

  // 分页请求
  const fetchData = async (page: number, size: number) => {
    setLoading(true);
    try {
      const res = await getCaregivers({ page, size });
      if (res.code === 200) {
        const { records, total } = res.data;
        setData(records);
        setPagination({ current: page, pageSize: size, total });
      } else {
        message.error(res.data.message);
      }
    } catch (err) {
      message.error('获取阿姨数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(1, 10);
  }, []);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    fetchData(pagination.current || 1, pagination.pageSize || 10);
  };

  // 查询阿姨
  const handleSearchCaregiver = async () => {
    setLoading(true);
    try {
      // 查询条件有阿姨ID或姓名之一即可
      const res = await getCaregiverById({
        caregiverId: caregiverId || null,  // 如果没有ID，传null
        name: name || '',  // 如果没有姓名，传空字符串
      });

      if (res.code === 200) {
        setData(res.data);
        setIsSearchMode(true);
        message.success('查询成功');
      } else {
        setData([]);
        message.warning('未找到该阿姨');
      }
    } catch (e) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置为分页模式
  const handleReset = () => {
    setCaregiverId('');
    setName('');
    setIsSearchMode(false);
    fetchData(1, pagination.pageSize || 10);
  };

  // 查询权限
  const handleQueryPermission = async (caregiverId: number) => {
    try {
      const res = await getCaregiverPermission({ caregiverId });
      if (res.code === 200) {
        setPermissionData(res.data); // 存放查询到的权限数据
        setCurrentCaregiverId(caregiverId); // 设置当前操作的阿姨ID
        setIsPermissionModalVisible(true); // 显示权限弹窗
        message.success('查询权限成功');
      } else {
        message.warning('查询权限失败');
      }
    } catch (e) {
      message.error('查询权限失败');
    }
  };

  // 切换权限（添加或删除）
  const handleTogglePermission = async (serviceTypeId: number, hasPermission: boolean) => {
    try {
      let res;
      if (hasPermission) {
        // 删除权限：需要找到对应的权限记录ID
        const permissionRecord = permissionData.find((permission: { serviceTypeId: number }) => permission.serviceTypeId === serviceTypeId);
        if (!permissionRecord || !permissionRecord.id) {
          message.error('找不到权限记录ID');
          return;
        }

        res = await deleteCaregiverPermission({
          id: permissionRecord.id,  // 使用权限记录的ID，不是serviceTypeId
        });

        if (res.code === 200) {
          message.success('删除权限成功');
          setPermissionData((prevData: any) =>
            prevData.filter((permission: { serviceTypeId: number }) => permission.serviceTypeId !== serviceTypeId)
          );
        } else {
          message.error(res.data.message || '删除权限失败');
        }
      } else {
        // 添加权限
        res = await addCaregiverPermission({
          caregiverServiceTypeList: [
            {
              caregiverId: currentCaregiverId,
              serviceTypeId,
            },
          ],
        });

        if (res.code === 200) {
          message.success('权限添加成功');
          setPermissionData((prevData: any) => [...prevData, { serviceTypeId, caregiverId: currentCaregiverId }]);
        } else {
          message.warning('权限添加失败');
        }
      }
    } catch (e) {
      message.error('权限操作失败');
    }
  };

  // 生成权限表格数据（包含所有服务类型）
  const generatePermissionTableData = () => {
    const allServiceTypes = [1, 2, 3, 4, 5];
    return allServiceTypes.map(serviceTypeId => {
      const hasPermission = permissionData.some((permission: { serviceTypeId: number }) => permission.serviceTypeId === serviceTypeId);
      const permissionRecord = permissionData.find((permission: { serviceTypeId: number }) => permission.serviceTypeId === serviceTypeId);

      return {
        key: serviceTypeId,
        serviceTypeId,
        serviceName: serviceTypeMap[serviceTypeId],
        hasPermission,
        createTime: permissionRecord?.createTime || '-',
        updateTime: permissionRecord?.updateTime || '-',
        id: permissionRecord?.id || '-',
      };
    });
  };

  const columns: ColumnsType<Caregiver> = [
    { title: 'ID', dataIndex: 'caregiverId', key: 'caregiverId' },
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '年龄', dataIndex: 'age', key: 'age' },
    { title: '性别', dataIndex: 'gender', key: 'gender', render: g => (g === 1 ? '男' : '女') },
    { title: '手机号', dataIndex: 'phoneNumber', key: 'phoneNumber' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    { title: '操作', render: (_, record) => (
      <Space>
        <Button
          type="link"
          onClick={() => handleQueryPermission(record.caregiverId)} // 查询权限
        >
          查询权限
        </Button>
      </Space>
    )},
  ];

  // 权限弹窗的表格列定义
  const permissionColumns = [
    { title: '权限ID', dataIndex: 'id', key: 'id' },
    { title: '服务类型', dataIndex: 'serviceName', key: 'serviceName' },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
    { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime' },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: { hasPermission: boolean; serviceTypeId: number }) => (
        <Button
          type="link"
          style={{
            color: record.hasPermission ? '#ff4d4f' : '#1890ff',
            fontWeight: 'bold',
          }}
          onClick={() => handleTogglePermission(record.serviceTypeId, record.hasPermission)}
        >
          {record.hasPermission ? '删除权限' : '添加权限'}
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="阿姨列表"
        extra={
          <Space>
            <Input
              placeholder="输入阿姨ID"
              value={caregiverId}
              onChange={(e) => setCaregiverId(e.target.value)}
              style={{ width: 200 }}
            />
            <Input
              placeholder="输入姓名"
              value={name}
              onChange={(e) => setName(e.target.value)}
              style={{ width: 200 }}
            />
            <Button type="primary" onClick={handleSearchCaregiver}>
              查询阿姨
            </Button>
            <Button onClick={handleReset}>重置</Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          rowKey="caregiverId"
          dataSource={data}
          loading={loading}
          pagination={isSearchMode ? false : pagination}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 查询权限的弹窗 */}
      <Modal
        title="阿姨权限"
        visible={isPermissionModalVisible}
        onCancel={() => setIsPermissionModalVisible(false)}
        footer={null}
        width={800} // 设置弹窗宽度
      >
        <Table
          rowKey="key"
          dataSource={generatePermissionTableData()}
          columns={permissionColumns}
          pagination={false}
        />
      </Modal>
    </div>
  );
};

export default CaregiverList;

import React, { useEffect, useState } from 'react'
import { Image, Button, Card, Row, Col, Empty , Radio, Divider, Input, App } from 'antd'
import ApiClient from '../api/api'
import { useParams, useNavigate } from 'react-router-dom'

const CaregiverAuditPage: React.FC = () => {
  const { message } = App.useApp()
  const { caregiverId } = useParams()
  const navigate = useNavigate()

  const [data, setData] = useState<any>(null)
  const [idCardStatus, setIdCardStatus] = useState(1)
  const [healthCertStatus, setHealthCertStatus] = useState(1)
  const [auditRemark, setAuditRemark] = useState('')

  const shouldShowRemark = idCardStatus === 0 || healthCertStatus === 0

  let shown = false

  const fetchData = async () => {
    if (!caregiverId) return
    try {
      const res = await ApiClient.post('caregiver/audit-images', {
        caregiverId: Number(caregiverId)
      })
      setData(res.data)
      setIdCardStatus(1)
      setHealthCertStatus(1)
      setAuditRemark('')
      if (!shown) {
        message.success('图片获取成功')
        shown = true
      }
    } catch (e) {
      console.error('图片获取失败:', e)
      message.error('获取失败，请检查后端或网络')
    }
  }
  

  // 页面加载后自动查数据
  useEffect(() => {
    fetchData()
  }, [caregiverId])

  const handleSubmit = async () => {
    if (!caregiverId) {
      message.warning('未获取到阿姨ID')
      return
    }

    if (idCardStatus === 1 && healthCertStatus === 1 && auditRemark.trim()) {
      message.error('全部通过时不能填写拒绝理由')
      return
    }

    if ((idCardStatus === 0 || healthCertStatus === 0) && !auditRemark.trim()) {
      message.error('有项拒绝时必须填写拒绝理由')
      return
    }

    try {
      const res = await ApiClient.put<any>('caregiver/putCaregiver', {
        caregiverId: Number(caregiverId),
        idCardVerification: idCardStatus,
        healthCertificateVerification: healthCertStatus,
        auditRemark: auditRemark
      })

      if (res.code === 200) {
        message.success('审核提交成功')
        navigate('/caregiver-audit') 
      } else {
        message.error('提交失败：' + (res.data.message || '未知错误'))
      }
    } catch (e: any) {
      console.error('提交异常：', e)
      message.error('网络异常：' + (e.message || '未知错误'))
    }
  }

  const renderImageCard = (title: string, url?: string) => (
    <Card title={title} style={{ width: 250, height: 300, textAlign: 'center' }} hoverable>
      {url ? (
        <Image
          src={url}
          alt={title}
          style={{ width: '100%', height: '200px', objectFit: 'cover', borderRadius: 6 }}
          preview={{ mask: '点击预览' }}
        />
      ) : (
        <Empty description="暂无图片" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </Card>
  )

  return (
    <div style={{ padding: 24 }}>
      <h2 style={{ marginBottom: 16 }}>阿姨审核页面</h2>

      <Row gutter={24} style={{ marginTop: 24 }}>
        <Col>{renderImageCard('身份证正面', data?.idCardFront)}</Col>
        <Col>{renderImageCard('身份证反面', data?.idCardBack)}</Col>
        <Col>{renderImageCard('健康证', data?.healthCertificate)}</Col>
      </Row>

      {data && (
        <>
          <Divider />
          <div style={{ marginTop: 16 }}>
            <h3>审核操作</h3>

            <div style={{ marginBottom: 16 }}>
              <span style={{ marginRight: 8 }}>身份证审核：</span>
              <Radio.Group value={idCardStatus} onChange={e => setIdCardStatus(e.target.value)}>
                <Radio value={1}>通过</Radio>
                <Radio value={0}>拒绝</Radio>
              </Radio.Group>
            </div>

            <div style={{ marginBottom: 16 }}>
              <span style={{ marginRight: 8 }}>健康证审核：</span>
              <Radio.Group value={healthCertStatus} onChange={e => setHealthCertStatus(e.target.value)}>
                <Radio value={1}>通过</Radio>
                <Radio value={0}>拒绝</Radio>
              </Radio.Group>
            </div>

            {shouldShowRemark && (
              <Input.TextArea
                placeholder="请填写拒绝理由"
                value={auditRemark}
                onChange={e => setAuditRemark(e.target.value)}
                rows={4}
                style={{ width: 600 }}
              />
            )}

            <Button type="primary" onClick={handleSubmit} style={{ marginTop: 24 }}>
              提交审核
            </Button>
          </div>
        </>
      )}
    </div>
  )
}

export default CaregiverAuditPage

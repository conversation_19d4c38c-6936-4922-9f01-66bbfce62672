import { useState } from 'react';
import { Form, Input, Button, Select, notification, Alert } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import ApiClient from "../api/api";

const { Option } = Select;

const RegisterAccount = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [alertInfo, setAlertInfo] = useState<any>({
    message: "",
    type: ""
  }); // 用于显示页面内提示

  const handleSubmit = async (values: any) => {
    setLoading(true);
    setAlertInfo({
      message: "",
      type: ""
    }); // 清除之前的提示

    try {
      const response = await ApiClient.post('adminUser/addAccount', values);

      if (response.code === 200) {
        // 方式1: 使用 notification (推荐)
        notification.success({
          message: '注册成功',
          description: '账户已成功创建！',
          placement: 'topRight',
          duration: 3,
          icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
        });

        // 设置页面内Alert显示
        setAlertInfo({
          type: 'success',
          message: '注册成功！账户已成功创建。'
        });

        // 清空表单
        form.resetFields();

        // 3秒后清除提示
        setTimeout(() => {
          setAlertInfo({
            message: "",
            type: ""
          });
        }, 3000);

      } else {
        const errorMsg = response.message || '注册失败';

        // 方式1: 使用 notification
        notification.error({
          message: '注册失败',
          description: errorMsg,
          placement: 'topRight',
          duration: 4,
          icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
        });

        // 设置页面内Alert显示
        setAlertInfo({
          type: 'error',
          message: `注册失败: ${errorMsg}`
        });
      }
    } catch (error: any) {
      console.error('请求异常:', error);

      let errorMessage = '注册失败，请重试';
      if (error.response) {
        errorMessage = error.response.data?.message || '服务器错误';
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络';
      }

      notification.error({
        message: '注册失败',
        description: errorMessage,
        placement: 'topRight',
        duration: 4,
      });

      // 设置页面内Alert显示
      setAlertInfo({
        type: 'error',
        message: errorMessage
      });

    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: 400, margin: 'auto', padding: '20px' }}>
      <h2>注册账号</h2>

      {/* 页面内提示 */}
      {alertInfo.message && (
        <Alert
          message={alertInfo.message}
          type={alertInfo.type}
          showIcon
          closable
          onClose={() => setAlertInfo({
            message: "",
            type: "success"
          })}
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        initialValues={{
          status: '1',
          accountPermission: '1'
        }}
      >
        <Form.Item
          name="username"
          label="用户名"
          rules={[
            { required: true, message: '请输入用户名' },
            { min: 3, message: '用户名至少3个字符' }
          ]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码长度不能少于 6 个字符' },
          ]}
        >
          <Input.Password placeholder="请输入密码" />
        </Form.Item>

        <Form.Item
          name="nickname"
          label="昵称"
          rules={[{ required: true, message: '请输入昵称' }]}
        >
          <Input placeholder="请输入昵称" />
        </Form.Item>

        <Form.Item
          name="status"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
        >
          <Select placeholder="选择状态">
            <Option value="1">启用</Option>
            <Option value="0">禁用</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="accountPermission"
          label="权限"
          rules={[{ required: true, message: '请选择权限' }]}
        >
          <Select placeholder="选择权限">
            <Option value="1">员工</Option>
            <Option value="2">管理员</Option>
          </Select>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} block>
            {loading ? '注册中...' : '注册'}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default RegisterAccount;

import React, { useEffect, useState } from 'react'
import { Table, Card, message, Input, Button, Space,Modal,Form,Input as AntdInput } from 'antd'
import type { TablePaginationConfig, ColumnsType } from 'antd/es/table'
import { getUsers, getUserById, putPassword } from '../api/user'
import type { User } from '../api/user'


const UserList: React.FC = () => {
  const [data, setData] = useState<any>([])
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState<string>(''); // 输入框内容

  //支付密码弹窗
  const [modal, modalContextHolder] = Modal.useModal();
  const [pwdModalOpen, setPwdModalOpen] = useState(false)
  const [pwdSubmitting, setPwdSubmitting] = useState(false)
  const [pwdForm] = Form.useForm()
  const [currentUserId, setCurrentUserId] = useState<number | null>(null)

  // 获取分页用户数据
  const fetchData = async (page: number, size: number) => {
    setLoading(true);
    try {
      const res = await getUserById({ userId: null, nickname: "" }); // 获取所有数据
      if (res.code === 200) {
        setData(res.data);  // 设置数据
        setPagination({ current: page, pageSize: size, total: res.data.code }); // 设置分页
      } else {
        message.error(res.message || '获取失败');
      }
    } catch (err) {
      message.error('获取用户数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(pagination.current || 1, pagination.pageSize || 10)
  }, [])



  // 表格分页变化时调用
  const handleTableChange = (pagination: TablePaginationConfig) => {
    fetchData(pagination.current || 1, pagination.pageSize || 10)
  }

  // 查询指定用户
  const handleSearch = async () => {
    const userId = parseInt(searchQuery);
    if (!isNaN(userId)) {
      // 如果是用户ID
      searchByUserId(userId);
    } else if (searchQuery.trim() !== "") {
      // 如果是用户名
      searchByUsername(searchQuery.trim());
    } else {
      message.warning('请输入有效的用户ID或用户名');
    }
  };

  // 按用户ID查询
  const searchByUserId = async (userId: number) => {
    setLoading(true);
    try {
      const res = await getUserById({ userId, nickname: "" });
      if (res.code === 200) {
        setData(res.data);
        setPagination({ current: 1, pageSize: 10, total: 1 });
        message.success('查询成功');
      } else {
        setData([]);
        setPagination({ current: 1, pageSize: 10, total: 0 });
        message.warning('未找到该用户');
      }
    } catch (e) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  // 按用户名查询
  const searchByUsername = async (username: string) => {
    setLoading(true);
    try {
      const res = await getUserById({ userId: null, nickname: username });
      if (res.code === 200) {
        setData(res.data);
        setPagination({ current: 1, pageSize: 10, total: res.data.code });
        message.success('查询成功');
      } else {
        setData([]);
        setPagination({ current: 1, pageSize: 10, total: 0 });
        message.warning('未找到该用户');
      }
    } catch (e) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置回默认分页
  const handleReset = () => {
    setSearchQuery('')
    fetchData(1, pagination.pageSize || 10)
  }

  // 第一步：不可逆提示（点击确认后再开表单）
  const confirmOpenPwdModal = (userId: number) => {
    modal.confirm({
      title: '修改支付密码（不可逆操作）',
      content: '该操作将直接为该用户设置/重置支付密码，无法撤销。请确认已经核实用户身份。',
      okText: '确认，继续',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: () => openPwdModal(userId)
    })
  }

  // 打开设置支付密码弹窗
  const openPwdModal = (userId: number) => {
    setCurrentUserId(userId)
    pwdForm.resetFields()
    setPwdModalOpen(true)
  }

  // 提交：二次确认（点击确认后才真正调用接口）
  const handlePwdSubmit = async () => {
    try {
      const values = await pwdForm.validateFields()
      const { payPassword, confirmPayPassword } = values
      if (payPassword !== confirmPayPassword) {
        message.error('两次输入的密码不一致')
        return
      }
      if (!currentUserId) {
        message.error('用户ID错误')
        return
      }
      modal.confirm({
        title: '二次确认',
        content: `将为用户ID：${currentUserId} 设置新的支付密码。确认继续？`,
        okText: '确认提交',
        cancelText: '取消',
        okButtonProps: { danger: true },
        onOk: async () => {
          setPwdSubmitting(true)
          try {
            const res = await putPassword({ userId: currentUserId, payPassword })
            if (res.code === 200) {
              message.success('支付密码更新成功')
              setPwdModalOpen(false)
              fetchData(pagination.current || 1, pagination.pageSize || 10)
            } else {
              message.error(res.message || '更新失败')
            }
          } catch {
            message.error('更新失败')
          } finally {
            setPwdSubmitting(false)
          }
        }
      })
    } catch (err: any) {
      if (!err?.errorFields) {
        message.error('设置失败')
      }
    }
  }
  // 表格列配置
  const columns: ColumnsType<User> = [
    { title: '用户ID', dataIndex: 'userId', key: 'userId' },
    { title: '手机号', dataIndex: 'phoneNumber', key: 'phoneNumber' },
    { title: '昵称', dataIndex: 'nickname', key: 'nickname' },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (val) => (val === 1 ? '启用' : '禁用')
    },
    { title: '注册时间', dataIndex: 'registrationTime', key: 'registrationTime' },
    { title: '用户来源',
      dataIndex: 'userSource',
      key: 'userSource', 
      render : (value:any) =>{
      switch(String(value)){
        case '1':
          return '广告牌';
        case '2':
          return '地推';
        case '3':
          return '公众号';
        case '4':
          return '老用户邀请';
        case '5':
          return '电梯广告';
        case '6':
          return '私域转发';
        default:
          return '其他';
      }
    }
    },
    { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime' },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 160,
      render: (_val, record) => (
        <Space>
          <Button type="link" onClick={() => confirmOpenPwdModal(record.userId)}>
            设置支付密码
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: 24 }}>
      {modalContextHolder}
      <Card
        title="用户列表"
        extra={
          <Space>
            <Input
              placeholder="输入用户ID/昵称"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ width: 200 }}
            />
            <Button onClick={handleSearch} type="primary">
              查询
            </Button>
            <Button onClick={handleReset}>重置</Button>
          </Space>
        }
      >
        <Table
          rowKey="userId"
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 设置支付密码弹窗 */}
      <Modal
        title="设置支付密码"
        open={pwdModalOpen}
        onOk={handlePwdSubmit}
        confirmLoading={pwdSubmitting}
        onCancel={() => setPwdModalOpen(false)}
        destroyOnHidden
      >
        <Form form={pwdForm} layout="vertical" preserve={false}>
          <Form.Item
            name="payPassword"
            label="新支付密码"
            rules={[
              { required: true, message: '请输入支付密码' }
              // { pattern: /^\d{6}$/, message: '请输入6位数字' } // 若需 6 位数字，打开此规则
            ]}
          >
            <AntdInput.Password placeholder="请输入支付密码" maxLength={32} />
          </Form.Item>

          <Form.Item
            name="confirmPayPassword"
            label="确认支付密码"
            rules={[{ required: true, message: '请确认支付密码' }]}
          >
            <AntdInput.Password placeholder="请再次确认支付密码" maxLength={32} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default UserList

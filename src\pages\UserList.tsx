import React, { useEffect, useState } from 'react'
import { Table, Card, message, Input, Button, Space } from 'antd'
import type { TablePaginationConfig, ColumnsType } from 'antd/es/table'
import { getUsers, getUserById } from '../api/user'
import type { User } from '../api/user'

const UserList: React.FC = () => {
  const [data, setData] = useState<any>([])
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState<string>(''); // 输入框内容

  // 获取分页用户数据
  const fetchData = async (page: number, size: number) => {
    setLoading(true);
    try {
      const res = await getUserById({ userId: null, nickname: "" }); // 获取所有数据
      if (res.code === 200) {
        setData(res.data);  // 设置数据
        setPagination({ current: page, pageSize: size, total: res.data.code }); // 设置分页
      } else {
        message.error(res.message || '获取失败');
      }
    } catch (err) {
      message.error('获取用户数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(pagination.current || 1, pagination.pageSize || 10)
  }, [])

  // 表格分页变化时调用
  const handleTableChange = (pagination: TablePaginationConfig) => {
    fetchData(pagination.current || 1, pagination.pageSize || 10)
  }

  // 查询指定用户
  const handleSearch = async () => {
    const userId = parseInt(searchQuery);
    if (!isNaN(userId)) {
      // 如果是用户ID
      searchByUserId(userId);
    } else if (searchQuery.trim() !== "") {
      // 如果是用户名
      searchByUsername(searchQuery.trim());
    } else {
      message.warning('请输入有效的用户ID或用户名');
    }
  };

  // 按用户ID查询
  const searchByUserId = async (userId: number) => {
    setLoading(true);
    try {
      const res = await getUserById({ userId, nickname: "" });
      if (res.code === 200) {
        setData(res.data);
        setPagination({ current: 1, pageSize: 10, total: 1 });
        message.success('查询成功');
      } else {
        setData([]);
        setPagination({ current: 1, pageSize: 10, total: 0 });
        message.warning('未找到该用户');
      }
    } catch (e) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  // 按用户名查询
  const searchByUsername = async (username: string) => {
    setLoading(true);
    try {
      const res = await getUserById({ userId: null, nickname: username });
      if (res.code === 200) {
        setData(res.data);
        setPagination({ current: 1, pageSize: 10, total: res.data.code });
        message.success('查询成功');
      } else {
        setData([]);
        setPagination({ current: 1, pageSize: 10, total: 0 });
        message.warning('未找到该用户');
      }
    } catch (e) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置回默认分页
  const handleReset = () => {
    setSearchQuery('')
    fetchData(1, pagination.pageSize || 10)
  }

  // 表格列配置
  const columns: ColumnsType<User> = [
    { title: '用户ID', dataIndex: 'userId', key: 'userId' },
    { title: '手机号', dataIndex: 'phoneNumber', key: 'phoneNumber' },
    { title: '昵称', dataIndex: 'nickname', key: 'nickname' },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (val) => (val === 1 ? '启用' : '禁用')
    },
    { title: '注册时间', dataIndex: 'registrationTime', key: 'registrationTime' },
    { title: '用户来源',
      dataIndex: 'userSource',
      key: 'userSource', 
      render : (value:any) =>{
      switch(String(value)){
        case '1':
          return '地推';
        case '2':
          return '广告';
        default:
          return '其他';
      }
    }
    },
    { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime' }
  ]

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="用户列表"
        extra={
          <Space>
            <Input
              placeholder="输入用户ID/昵称"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ width: 200 }}
            />
            <Button onClick={handleSearch} type="primary">查询</Button>
            <Button onClick={handleReset}>重置</Button>
          </Space>
        }
      >
        <Table
          rowKey="userId"
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  )
}

export default UserList

import ApiClient from "./api"


// ✅ 获取总阿姨数量
export const getCaregiverTotal = async (): Promise<any> => {
  const response = await ApiClient.get('caregiver/total')
  // ✅ 后端返回的是 { code: 200, data: 数字 }
  if (response.code === 200) {
    return response.data
  } else {
    throw new Error(response.message || '获取阿姨数量失败')
  }
}

// ✅ 获取总用户数量
export const getUserTotal = async (): Promise<any> => {
    const response = await ApiClient.get('user/total')
    if (response.code === 200) {
      return response.data
    } else {
      throw new Error(response.message || '获取用户数量失败')
    }
  }

  // ✅ 获取全部收入数据
export const getTotalIncomeStats = async (): Promise<any> => {
    const response = await ApiClient.post('order/statistics/income/all')
    if (response.code === 200) {
      return response.data
    } else {
      throw new Error(response.message || '获取收入失败')
    }
  }

 // ✅ 查询年收入
export const getYearIncomeStats = async (year: number): Promise<any> => {
    const response = await ApiClient.post('order/statistics/income/year', { year })
    if (response.code === 200) {
      return response.data
    } else {
      throw new Error(response.message || '查询年收入失败')
    }
  }
  
  // ✅ 查询月收入
export const getMonthIncomeStats = async (year: number, month: number): Promise<any> => {
    const response = await ApiClient.post('order/statistics/income/month', {
      year,
      month,
    })
    if (response.code === 200) {
      return response.data
    } else {
      throw new Error(response.message || '查询月收入失败')
    }
  }
  
  // ✅ 获取日收入
  export const getDayIncomeStats = async (date: string): Promise<any> => {
    const response = await ApiClient.post('order/statistics/income/day', { date })
    if (response.code === 200) return response.data
    throw new Error('获取日收入失败')
  }

  // ✅ 查询年支出
export const getYearExpenseStats = async (year: number): Promise<any> => {
  const response = await ApiClient.post('order/statistics/expense/year', { year })
  if (response.code === 200) return response.data
  throw new Error(response.message || '获取年支出失败')
}

// ✅ 查询月支出
export const getMonthExpenseStats = async (year: number, month: number): Promise<any> => {
  const response = await ApiClient.post('order/statistics/expense/month', {
    year,
    month,
  })
  if (response.code === 200) return response.data
  throw new Error(response.message || '获取月支出失败')
}

// ✅ 查询日支出
export const getDayExpenseStats = async (date: string): Promise<any> => {
  const response = await ApiClient.post('order/statistics/expense/day', { date })
  if (response.code === 200) return response.data
  throw new Error(response.message || '获取日支出失败')
}

// ✅ 查询全部收益
export const getTotalProfitStats = async (): Promise<any> => {
  const response = await ApiClient.post('order/statistics/profit/all')
  if (response.code === 200) return response.data
  throw new Error(response.message || '获取总收益失败')
}

// ✅ 年收益
export const getYearProfitStats = async (year: number): Promise<any> => {
  const response = await ApiClient.post('order/statistics/profit/year', { year })
  if (response.code === 200) return response.data
  throw new Error(response.message || '查询年收益失败')
}

// ✅ 月收益
export const getMonthProfitStats = async (year: number, month: number): Promise<any> => {
  const response = await ApiClient.post('order/statistics/profit/month', { year, month })
  if (response.code === 200) return response.data
  throw new Error(response.message || '查询月收益失败')
}

// ✅ 日收益
export const getDayProfitStats = async (date: string): Promise<any> => {
  const response = await ApiClient.post('order/statistics/profit/day', { date })
  if (response.code === 200) return response.data
  throw new Error(response.message || '查询日收益失败')
}

// ✅ 查询服务类型订单数量
export const getOrderTypeCounts = async (): Promise<any> => {
  const response = await ApiClient.get('order/type/counts')
  if (response.code === 200) return response.data
  throw new Error(response.message || '获取服务类型订单统计失败')
}

// ✅ 查询地区下单类型数量（根据地址和服务类型条件）
export const getRegionOrderTypeCount = async (params: {
  area: string;  // 使用 area 作为参数名，和后端一致
  serviceTypeId: number;
}): Promise<any> => {
  try {
    const response = await ApiClient.post('order/region-order-type-count', params);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response.message || '获取地区下单类型数量失败');
  } catch (error) {
    console.error("Error in getRegionOrderTypeCount: ", error);
    throw new Error('获取地区下单类型数量失败');
  }
}
// 地区下单类型数量统计（带条件）
// export function getRegionOrderTypeCount(params: Record<string, any>) {
//   return axios.post('/region-order-type-count', params).then(res => res.data.data)
// }

// 获取总支出统计
export const getTotalExpenseStats = async (): Promise<any> => {
  const response = await ApiClient.post('order/statistics/expense/all')
  if (response.code === 200) return response.data
  throw new Error(response.message || '获取总支出失败')
}

export const getToolIncome = async (): Promise<any> => {
  const response = await ApiClient.post('cleaningItemShopOrder/toolIncome')
  if (response.code === 200) return response.data
  throw new Error(response.message || '获取工具收入失败')
}

export const getTotalPlatformProfit = async (): Promise<any> => {
  const response = await ApiClient.get('walletTransaction/totalPlatformProfit')
  if (response.code === 200) return response.data
  throw new Error(response.message || '获取平台总收益失败')
}


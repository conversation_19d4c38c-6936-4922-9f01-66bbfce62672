import ApiClient from "./api"

export interface GiftCouponProbability {
  id: number
  couponId: number
  releaseProbability: number
  couponTitle?: string
  couponDescription?: string
}

export interface GiftCouponProbabilityDTO {
  id: number
  releaseProbability?: number
}

export function getGiftCouponProbability() {
  return ApiClient.get<{ code: number; message: string; data: GiftCouponProbability[] }>(
    'giftCouponProbability/getGiftCouponProbability'
  )
}

export function getGiftCouponProbabilityById(data: GiftCouponProbabilityDTO) {
  return ApiClient.get<{ code: number; message: string; data: GiftCouponProbability }>(
    'giftCouponProbability/getGiftCouponProbabilityById',
    data 
  )
}

export function addGiftCouponProbability(data: GiftCouponProbabilityDTO) {
  return ApiClient.post<{ code: number; message: string; data: number }>(
    'giftCouponProbability/postGiftCouponProbability',
    data
  )
}

export function updateGiftCouponProbability(data: GiftCouponProbabilityDTO) {
  return ApiClient.put<{ code: number; message: string; data: number }>(
    'giftCouponProbability/putGiftCouponProbability',
    data
  )
}

export function deleteGiftCouponProbability(data: GiftCouponProbabilityDTO) {
  return ApiClient.delete<{ code: number; message: string; data: number }>(
    'giftCouponProbability/deleteGiftCouponProbability',
    data
  )
}


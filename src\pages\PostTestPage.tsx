import React, { useEffect, useState } from 'react'
import {
  Table, Button, Modal, Form, Input,
  Space, Popconfirm, Card,
  App
} from 'antd'
import apiClient from '../api/api'
import dayjs from 'dayjs'

interface Message {
  id?: number
  title: string
  content: string
  createTime?: string
  updateTime?: string
}

const NotificationPage: React.FC = () => {
  const { message } = App.useApp()
  const [data, setData] = useState<Message[]>([])
  const [loading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [pageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [form] = Form.useForm()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<Message | null>(null)

  // 获取通知列表
  const fetchData = async () => {
    setLoading(true)
    try {
      const res = await apiClient.post<any>('message/getMessage', {
        page, size: pageSize
      })
      const result = res.data
      setData(result.records)
      setTotal(result.total)
    } catch (e) {
      message.error('加载失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [page])

  // 打开弹窗（新增或编辑）
  const openModal = (item?: Message) => {
    setEditingItem(item || null)
    setIsModalOpen(true)
    if (item) {
      form.setFieldsValue(item)
    } else {
      form.resetFields()
    }
  }

  // 提交表单（新增或修改）
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      let res

      if (editingItem?.id) {
        // 修改
        res = await apiClient.put('message/putMessage', {
          id: editingItem.id,
          ...values
        })
      } else {
        // 新增
        res = await apiClient.post('message/postMessage', values)
      }

      if (res.code === 200) {
        message.success(editingItem ? '修改成功' : '新增成功')
        setIsModalOpen(false)
        fetchData()
      } else {
        message.error(res.message || '操作失败')
      }
    } catch {
      // 表单校验失败
    }
  }

  // 删除通知
  const handleDelete = async (id: number) => {
    try {
      await apiClient.delete('message/deleteMessage', {
         id 
      })
      message.success('删除成功')
      fetchData()
    } catch {
      message.error('删除失败')
    }
  }

   // 下发通知
   const handleSendNotification = async (messageId: number, userType: number) => {
    try {
      const res = await apiClient.post('userMessage/sendNotification', {
        messageId,
        userType,
      })

      if (res.code === 200) {
        console.log(111);
        
        // 下发成功提示
        message.success('通知已成功下发')

        // 弹窗提示成功
      // Modal.success({
      //   title: '通知下发成功',
      //   content: `通知 "${messageId}" 已成功下发给 ${userType === 1 ? '用户' : '阿姨'}.`,
      // });
      } else {
        console.log(22);
        
        message.error('通知下发失败')
      }
    } catch (e) {
      message.error('下发通知失败')
    }
  }

  return (
    <div style={{ padding: 24 }}>
      <Card title="系统通知发布" extra={(
        <Button type="primary" onClick={() => openModal()}>新增通知</Button>
      )}>
        <Table
          rowKey="id"
          dataSource={data}
          loading={loading}
          pagination={{
            current: page,
            pageSize: pageSize,
            total: total,
            onChange: (p) => setPage(p)
          }}
        >
          <Table.Column title="ID" dataIndex="id" />
          <Table.Column title="标题" dataIndex="title" />
          <Table.Column title="内容" dataIndex="content" />
          <Table.Column
            title="创建时间"
            dataIndex="createTime"
            render={(text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'}
          />
          <Table.Column
            title="操作"
            render={(_, record: Message) => (
              <Space>
                <Button type="link" onClick={() => openModal(record)}>编辑</Button>
                <Popconfirm
                  title="确定要删除这条通知吗？"
                  onConfirm={() => handleDelete(record.id!)}
                  okText="确认"
                  cancelText="取消"
                >
                  <Button type="link" danger>删除</Button>
                </Popconfirm>
                {/* 下发按钮 */}
                <Button 
                  type="link" 
                  onClick={() => handleSendNotification(record.id!, 1)} // 1表示下发给用户
                >
                  下发给用户
                </Button>
                <Button 
                  type="link" 
                  onClick={() => handleSendNotification(record.id!, 2)} // 2表示下发给阿姨
                >
                  下发给阿姨
                </Button>
              </Space>
            )}
          />
        </Table>
      </Card>

      {/* 新增/编辑弹窗 */}
      <Modal
        title={editingItem ? '编辑通知' : '新增通知'}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleSubmit}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="通知标题"
            name="title"
            rules={[{ required: true, message: '请输入通知标题' }]}
          >
            <Input placeholder="请输入标题" />
          </Form.Item>
          <Form.Item
            label="通知内容"
            name="content"
            rules={[{ required: true, message: '请输入通知内容' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入通知内容" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default NotificationPage
{"name": "admindashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "start": "HOST=0.0.0.0 react-scripts start"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.0", "axios": "^1.10.0", "echarts": "^5.6.0", "lucide-react": "^0.534.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-tsparticles": "^2.12.2", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.2.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.5.2", "connect-history-api-fallback": "^2.0.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}
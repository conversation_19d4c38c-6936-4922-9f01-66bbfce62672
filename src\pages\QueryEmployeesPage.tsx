import React, { useState, useEffect } from 'react';
import { Table, Button, message, Input, Space } from 'antd';
import { getQueryEmployees, deleteAccount } from '../api/adminUser';

const QueryEmployeesPage: React.FC = () => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState<any>({
    current: 1,
    pageSize: 5,
    total: 0,
  });
  const [searchQuery, setSearchQuery] = useState<string>('');

  const fetchEmployees = async (page: number, size: number) => {
    setLoading(true);
    try {
      const response = await getQueryEmployees({ page, size, searchQuery });
      console.log('API响应:', response); // 添加调试日志
      
      // 修复数据处理 - 根据实际返回的数据结构调整
      if (response && response.records) {
        setData(response.records);
        setPagination({
          current: page,
          pageSize: size,
          total: response.total,
        });
      } else if (Array.isArray(response)) {
        // 如果直接返回数组
        setData(response);
        setPagination({
          current: page,
          pageSize: size,
          total: response.length,
        });
      } else {
        setData([]);
        setPagination({
          current: page,
          pageSize: size,
          total: 0,
        });
      }
    } catch (error: any) {
      console.error('查询员工失败:', error);
      message.error(error.message || '查询失败');
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEmployees(pagination.current, pagination.pageSize);
  }, []); // 只在组件挂载时执行一次

  // 如果需要在分页变化时重新获取数据，可以这样写：
  // useEffect(() => {
  //   fetchEmployees(pagination.current, pagination.pageSize);
  // }, [pagination.current, pagination.pageSize]);

  const handleDelete = async (id: number) => {
    try {
      const response = await deleteAccount({ id });
      console.log('删除响应:', response); // 添加调试日志
      
      if (response && response.code === 200) {
        message.success('删除成功');
        // 删除成功后重新获取当前页数据
        fetchEmployees(pagination.current, pagination.pageSize);
      } else {
        message.error(response?.message || '删除失败');
      }
    } catch (error: any) {
      console.error('删除失败:', error);
      message.error(error.message || '删除失败');
    }
  };

  const handleSearch = () => {
    fetchEmployees(1, pagination.pageSize);  // 查询时重置分页为1
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '密码',
      dataIndex: 'password',
      key: 'password',
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      key: 'nickname',
    },
    {
      title: '账号状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space size="middle">
          <Button 
            type="link" 
            danger
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Input
        placeholder="请输入用户名"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        style={{ width: 200, marginBottom: 16 }}
      />
      <Button type="primary" onClick={handleSearch} style={{ marginLeft: 16 }}>
        搜索
      </Button>
      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={pagination}
        rowKey={(record, index) => `${record.id}-${index}`}
        onChange={(pagination) => setPagination(pagination)}
      />
    </div>
  );
};

export default QueryEmployeesPage;

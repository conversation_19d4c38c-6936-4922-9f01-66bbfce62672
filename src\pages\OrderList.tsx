import React, { useEffect, useState } from 'react'
import { Card, Table, message, Input, Button, Space, Modal } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import apiClient from '../api/api'
import dayjs from 'dayjs'

// 订单类型
interface Order {
  orderId: number
  orderNumber: string
  userId: number
  userName:string
  caregiversId: number
  caregiverName:string
  addressId: number
  status: number
  serviceTypeId: number
  appointmentTime: string
  orderCompletionTime: string
  totalPrice: number
  serviceVerificationImages: string[];  // 存放图片URL
}

// 返回格式
interface OrderResponse {
  records: Order[]
  total: number
  size: number
  current: number
}

const OrderList: React.FC = () => {
  const [data, setData] = useState<Order[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const [searchKeyword, setSearchKeyword] = useState<string>('') // 输入的查询关键字
  const [isSearchMode, setIsSearchMode] = useState(false) // 是否为查询模式
  const [orderDetail, setOrderDetail] = useState<any>(null) // 当前查看的订单详情
  const [modalVisible, setModalVisible] = useState(false) // 控制弹出框显示

  // 服务类型映射
  const serviceTypeMap: Record<number, string> = {
    1: '普通保洁',
    2: '开荒保洁', 
    3: '玻璃清洗',
    4: '机器拆洗',
    5: '企业保洁'
  }

  // 订单状态映射
  const statusMap: Record<number, string> = {
    0: '等待接单',
    1: '已预约',
    2: '进行中', 
    3: '订单完成',
    4: '已取消支付',
    5: '退款中',
    6: '已退款',
    7: '待支付',
    8: '服务完成',
    9: '拒绝预约',
    10: '修改订单中'
  }

  // 表格列定义
  const columns: ColumnsType<Order> = [
    { title: '订单编号', dataIndex: 'orderNumber' },
    { title: '用户姓名', dataIndex: 'userName' },
    { title: '阿姨姓名', dataIndex: 'caregiverName' },
    { 
      title: '服务类型', 
      dataIndex: 'serviceTypeId',
      render: (serviceTypeId: number) => serviceTypeMap[serviceTypeId] || `未知类型(${serviceTypeId})`
    },
    { 
      title: '状态', 
      dataIndex: 'status',
      render: (status: number) => statusMap[status] || `未知状态(${status})`
    },
    {
      title: '预约时间',
      dataIndex: 'appointmentTime',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '完成时间',
      dataIndex: 'orderCompletionTime',
      render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '--',
    },
    { title: '总价(元)', dataIndex: 'totalPrice' },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      render: (_, record) => (
        <Button type="link" onClick={() => handleViewOrder(record)}>查看</Button>
      ),
    }
  ]

  // 分页获取订单数据
  const fetchOrders = async (pageNum: number, size: number) => {
    setLoading(true)
    try {
      const res = await apiClient.post<any>('order/getOrder', {
        page: pageNum,
        size: size,
      })

      if (res.code === 200) {
        const result: OrderResponse = res.data
        setData(result.records)
        setTotal(result.total)
        setPage(result.current)
        setPageSize(result.size)
      } else {
        message.error('获取订单失败: ' + res.message)
      }
    } catch (error) {
      message.error('请求失败: ' + (error as any).message)
    } finally {
      setLoading(false)
    }
  }

  // 查询指定订单
  const handleSearchOrder = async () => {
    if (!searchKeyword || searchKeyword.trim() === '') {
      return message.warning('请输入订单编号、用户姓名或阿姨姓名')
    }

    setLoading(true)
    try {
      const requestData = {
        orderNumber: searchKeyword.trim(),
        userName: searchKeyword.trim(), 
        caregiverName: searchKeyword.trim()
      }
      
      console.log('发送的请求参数:', requestData)
      
      const res = await apiClient.post<any>('order/getOrderId', requestData)
      
      console.log('完整响应:', res)
      console.log('响应状态码:', res.code)
      console.log('响应数据:', res.data)
      console.log('响应数据类型:', typeof res.data)
      console.log('响应数据长度:', Array.isArray(res.data) ? res.data.length : '不是数组')
      
      if (res.code === 200) {
        const searchData = res.data || []
        console.log('处理后的搜索数据:', searchData)
        
        setData(searchData)
        setIsSearchMode(true)
        
        if (searchData.length > 0) {
          message.success(`查询成功，找到 ${searchData.length} 条记录`)
        } else {
          message.warning('未找到匹配的订单')
        }
      } else {
        console.log('响应码不是200:', res.code, res.message)
        setData([])
        message.warning('查询失败: ' + (res.message || ''))
      }
    } catch (err) {
      console.error('搜索错误:', err)
      message.error('查询失败: ' + (err as any).message)
      setData([])
    } finally {
      setLoading(false)
    }
  }

   // 重置为分页模式
   const handleReset = () => {
    setSearchKeyword('')
    setIsSearchMode(false)
    fetchOrders(1, pageSize)
  }

  // 查看订单详情
  const handleViewOrder = async (record: Order) => {
    setOrderDetail(record); // 设置当前查看的订单详情
    setModalVisible(true);  // 显示订单详情弹窗
    
    try {
      // 请求后端获取订单详情，包括图片
      const res = await apiClient.post<any>('order/orderId', {
        orderId: record.orderId  // 发送订单ID
      });
  
      if (res.code === 200) {
        const images = res.data[0].serviceVerificationImages;  // 假设返回的数据包含图片数组
        console.log(images);
        
        setOrderDetail((prevDetail:any) => (prevDetail ? {
          ...prevDetail,
          serviceVerificationImages: images,  // 将图片数据添加到订单详情中
        } : prevDetail));
      } else {
        message.error('获取图片失败: ' + res.data.message);
      }
    } catch (err) {
      message.error('请求失败');
    }
  };

  // 关闭订单详情弹窗
  const handleCancelModal = () => {
    setModalVisible(false)
  }

  useEffect(() => {
    if (!isSearchMode) {
      fetchOrders(page, pageSize)
    }
  }, [page, pageSize, isSearchMode])

  return (
    <Card
      title="订单列表"
      extra={
        <Space>
        <Input
          placeholder="输入订单编号/用户姓名/阿姨姓名"
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          style={{ width: 220 }}
        />
        <Button type="primary" onClick={handleSearchOrder}>查询订单</Button>
        <Button onClick={handleReset}>重置</Button>
      </Space>
      }
    >
      <Table
        rowKey="orderId"
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={
          isSearchMode
            ? false
            : {
                current: page,
                pageSize: pageSize,
                total: total,
                onChange: (p, ps) => {
                  setPage(p)
                  setPageSize(ps)
                }
              }
        }
        scroll={{ x: 1200 }}
      />

      {/* 订单详情弹窗 */}
      <Modal
        title="订单详情"
        visible={modalVisible}
        onCancel={handleCancelModal}
        footer={null}
        width={800}
      >
        {orderDetail && (
          <div>
            <p><strong>订单编号:</strong> {orderDetail.orderNumber}</p>
            <p><strong>用户姓名:</strong> {orderDetail.userName}</p>
            <p><strong>阿姨姓名:</strong> {orderDetail.caregiverName}</p>
            <p><strong>服务类型:</strong> {serviceTypeMap[orderDetail.serviceTypeId] || `未知类型(${orderDetail.serviceTypeId})`}</p>
            <p><strong>订单状态:</strong> {statusMap[orderDetail.status] || `未知状态(${orderDetail.status})`}</p>
            <p><strong>预约时间:</strong> {dayjs(orderDetail.appointmentTime).format('YYYY-MM-DD HH:mm')}</p>
            <p><strong>完成时间:</strong> {orderDetail.orderCompletionTime ? dayjs(orderDetail.orderCompletionTime).format('YYYY-MM-DD HH:mm') : '--'}</p>
            <p><strong>总价(元):</strong> {orderDetail.totalPrice}</p>
            <p><strong>图片:</strong></p>
            <div>
              {orderDetail.serviceVerificationImages?.map((image:any, index:number) => (
                <img key={index} src={image.imageUrl} alt={`verification-${index}`} style={{ width: 150, marginRight: 10 }} />
              ))}
            </div>
          </div>
        )}
      </Modal>
    </Card>
  )
}

export default OrderList

import React, { useState } from 'react'
import { Card, Input, Button, Typography, message, Al<PERSON>, Space } from 'antd'
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons'
import { login } from '../api/loginApi'
import { useNavigate } from 'react-router-dom'

const { Title, Text } = Typography

const LoginPage: React.FC = () => {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const navigate = useNavigate()

  const handleLogin = async () => {
    if (!username.trim() || !password.trim()) {
      setErrorMessage('请输入用户名和密码')
      return message.warning('请输入用户名和密码')
    }
    
    setErrorMessage('')
    setLoading(true)
    
    try {
      const res = await login({ username, password })
      
      if (res.code == 200) {
        const token = res.data.tokenValue
        const accountPermission = res.data.accountPermission
        const userId = res.data.id
        
        localStorage.setItem('token', token)
        localStorage.setItem('accountPermission', String(accountPermission))
        localStorage.setItem('userId', String(userId))
        
        message.success('登录成功')
        navigate('/dashboard')
      } else {
        const errorMsg = res.message || '登录失败'
        setErrorMessage(errorMsg)
        message.error(errorMsg)
      }
    } catch (e: any) {
      console.error('登录异常:', e)
      let errorMsg = '网络异常，请稍后重试'
      
      if (e.response?.data?.message) {
        errorMsg = e.response.data.message
      } else if (e.message) {
        errorMsg = '登录失败：' + e.message
      }
      
      setErrorMessage(errorMsg)
      message.error(errorMsg)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="login-container">
      <div className="login-background"></div>
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <div className="login-logo">
              <div className="logo-icon">🏢</div>
            </div>
            <Title level={2} className="login-title">企业后台管理系统</Title>
            <Text type="secondary">欢迎登录管理平台</Text>
          </div>
          
          <div className="login-form">
            {errorMessage && (
              <Alert
                message={errorMessage}
                type="error"
                showIcon
                closable
                onClose={() => setErrorMessage('')}
                style={{ marginBottom: 24 }}
              />
            )}
            
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <Input
                size="large"
                prefix={<UserOutlined />}
                placeholder="请输入用户名"
                value={username}
                onChange={e => setUsername(e.target.value)}
              />
              <Input.Password
                size="large"
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                value={password}
                onChange={e => setPassword(e.target.value)}
                onPressEnter={handleLogin}
              />
              <Button 
                type="primary" 
                size="large" 
                block 
                loading={loading}
                onClick={handleLogin}
                icon={<LoginOutlined />}
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default LoginPage

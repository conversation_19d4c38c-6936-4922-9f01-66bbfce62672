import React, { useEffect, useState } from 'react'
import { Card, Table, message, Input, Button, Space } from 'antd'
import apiClient from '../api/api'

interface RefundRequest {
  orderId: number
  userId: number
  nickname:number
  amount: number
  reason: string
  status: number
  createTime: string
  rejectReason?: string
}

const RefundApprovalPage: React.FC = () => {
  const [data, setData] = useState<RefundRequest[]>([])
  const [loading, setLoading] = useState(false)
  const [refundInputs, setRefundInputs] = useState<{
    [key: number]: { amount: string; reason: string }
  }>({})

  const fetchData = async () => {
    setLoading(true)
    try {
      const res = await apiClient.post<any>('order/getStatus', {
        status: 5
      })
      const records = res.data?.records || []
      setData(records)

      // 初始化输入框值
      const initialInputs: { [key: number]: { amount: string; reason: string } } = {}
      records.forEach((item: RefundRequest) => {
        initialInputs[item.orderId] = {
          amount: item.amount.toString(),
          reason: ''
        }
      })
      setRefundInputs(initialInputs)
    } catch (err: any) {
      message.error('加载失败：' + (err.message || '未知错误'))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleInputChange = (orderId: number, field: 'amount' | 'reason', value: string) => {
    setRefundInputs(prev => ({
      ...prev,
      [orderId]: {
        ...prev[orderId],
        [field]: value
      }
    }))
  }

  const handleApprove = async (orderId: number) => {
    const input = refundInputs[orderId]
    const refundAmount = parseFloat(input?.amount || '0')

    if (isNaN(refundAmount) || refundAmount <= 0) {
      message.warning('请输入有效的退款金额')
      return
    }

    try {
      await apiClient.put('order/putStatus', {
        orderId,
        status: 6,
        refundAmount: refundAmount,
        refundReason: input.reason // 前端提交理由（后端目前可忽略）
      })
      message.success('退款成功')
      fetchData()
    } catch (err: any) {
      message.error('退款失败：' + (err.message || ''))
    }
  }

  const handleReject = async (orderId: number) => {
    const input = refundInputs[orderId]
    if (!input.reason.trim()) {
      message.warning('请填写拒绝理由')
      return
    }

    try {
      await apiClient.put('order/putStatus', {
        orderId,
        status: 2,
        rejectReason: input.reason
      })
      message.success('已拒绝退款')
      fetchData()
    } catch (err: any) {
      message.error('拒绝失败：' + (err.message || ''))
    }
  }

  const columns = [
    { title: '订单ID', dataIndex: 'orderId', key: 'orderId' },
    { title: '用户昵称', dataIndex: 'userId', key: 'userId' },
    { title: '订单金额', dataIndex: 'amount', key: 'amount' },
    { title: '拒绝退款原因', dataIndex: 'rejectReason', key: 'rejectReason' },
    { title: '申请时间', dataIndex: 'createTime', key: 'createTime' },
    {
      title: '退款金额',
      key: 'refundAmountInput',
      render: (_: any, record: RefundRequest) => (
        <Input
          type="number"
          value={refundInputs[record.orderId]?.amount || ''}
          onChange={(e) => handleInputChange(record.orderId, 'amount', e.target.value)}
          style={{ width: 100 }}
        />
      )
    },
    {
      title: '退款理由',
      key: 'refundReasonInput',
      render: (_: any, record: RefundRequest) => (
        <Input
          value={refundInputs[record.orderId]?.reason || ''}
          onChange={(e) => handleInputChange(record.orderId, 'reason', e.target.value)}
          placeholder="退款说明（选填）"
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: RefundRequest) => (
        <Space>
          <Button type="primary" onClick={() => handleApprove(record.orderId)}>同意</Button>
          <Button danger onClick={() => handleReject(record.orderId)}>拒绝</Button>
        </Space>
      )
    }
  ]

  return (
    <Card title="退款审批列表">
      <Table
        rowKey="orderId"
        loading={loading}
        dataSource={data}
        columns={columns}
        pagination={{ pageSize: 10 }}
      />
    </Card>
  )
}

export default RefundApprovalPage

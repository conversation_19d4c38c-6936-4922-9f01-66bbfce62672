import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import AppLayout from './layout/Layout'
import Dashboard from './pages/Dashboard'
import OrderList from './pages/OrderList'
import CreateCouponPage from './pages/CreateCouponPage'
import CouponTypePage from './pages/CouponTypePage'
import GiftCouponProbabilityPage from './pages/GiftCouponProbabilityPage'
import CaregiverList from './pages/CaregiverList'
import UserList from './pages/UserList'
import PostTestPage from './pages/PostTestPage'
import CaregiverAuditListPage from './pages/CaregiverAuditListPage'
import CaregiverAuditDetailPage from './pages/CaregiverAuditDetailPage'
import LoginPage from './pages/LoginPage'
import RequireAuth from './components/RequireAuth'
import CommissionRatePage from './pages/CommissionRatePage'
import RefundApprovalPage from './pages/RefundApprovalPage'
import SysPicturePage from './pages/SysPicturePage'
import Complaint from './pages/Complaint'
import AddAccount from'./pages/AddAccount'
import CleaningItemShopOrderPage from './pages/CleaningItemShopOrderPage'
import UserVipPage from './pages/UserVipPage'
import QueryEmployeesPage from './pages/QueryEmployeesPage'
import SalaryManagement from './pages/SalaryManagement'
import RobotSupportPage from './pages/RobotSupportPage'

const UserPage = () => <div>这里是用户管理页面</div>

const App: React.FC = () => {
  return (
    <Routes>
      <Route path="/login" element={<LoginPage />} />
      <Route path="/" element={<AppLayout />}>
        <Route index element={<Navigate to="/dashboard" />} />
        <Route path="/dashboard" element={<RequireAuth><Dashboard /></RequireAuth>} />
        <Route path="/users" element={<RequireAuth><UserPage /></RequireAuth>} />
        <Route path="/orders" element={<RequireAuth><OrderList /></RequireAuth>} />
        <Route path="/coupons/create" element={<RequireAuth><CreateCouponPage /></RequireAuth>} />
        <Route path="/coupons/types" element={<RequireAuth><CouponTypePage /></RequireAuth>} />
        <Route path="/coupons/gift-probability" element={<RequireAuth><GiftCouponProbabilityPage /></RequireAuth>} />
        <Route path="/caregiver-list" element={<RequireAuth><CaregiverList /></RequireAuth>} />
        <Route path="/user-list" element={<RequireAuth><UserList /></RequireAuth>} />
        <Route path="/notification" element={<RequireAuth><PostTestPage /></RequireAuth>} />
        <Route path="/caregiver-audit" element={<RequireAuth><CaregiverAuditListPage /></RequireAuth>} />
        <Route path="/caregiver-audit/:caregiverId" element={<RequireAuth><CaregiverAuditDetailPage /></RequireAuth>} />
        <Route path="/commission-rate" element={<RequireAuth><CommissionRatePage /></RequireAuth>} />
        <Route path="/refund-approval" element={<RequireAuth><RefundApprovalPage /></RequireAuth>} />
        <Route path="/sys-picture" element={<RequireAuth><SysPicturePage /></RequireAuth>} />
        <Route path="/complaints" element={<RequireAuth><Complaint /></RequireAuth>} />
        <Route path="/addAccount" element={<RequireAuth><AddAccount/></RequireAuth>}/>
        <Route path="/cleaning-orders" element={<RequireAuth><CleaningItemShopOrderPage /></RequireAuth>} />
        <Route path="/user-vip" element={<RequireAuth><UserVipPage /></RequireAuth>} />
        <Route path="/employee-list" element={<RequireAuth><QueryEmployeesPage /></RequireAuth>} />
        <Route path="/salary-management" element={<RequireAuth><SalaryManagement /></RequireAuth>} />
        <Route path="/robot-support" element={<RequireAuth><RobotSupportPage /></RequireAuth>} />
      </Route>

      <Route path="*" element={<Navigate to="/" />} />
    </Routes>
  )
}

export default App
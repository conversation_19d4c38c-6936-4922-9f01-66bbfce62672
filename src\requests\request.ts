// src/utils/request.ts
import axios from 'axios'
import { message } from 'antd'

const request = axios.create({
  baseURL: '/api', // 你可根据 vite.config.ts 配置来调整代理前缀
  timeout: 10000,
})

// 请求拦截器（可自动加 token）
request.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器（统一处理 code !== 200 的情况）
request.interceptors.response.use(
  (response) => {
    const res = response.data
    if (res.code !== 200) {
      message.error(res.message || '请求失败')
      return Promise.reject(res)
    }
    return res
  },
  (error) => {
    // 处理 401 未授权错误
    if (error.response?.status === 401) {
      message.error('登录已过期，请重新登录')
      localStorage.removeItem('token')
      localStorage.removeItem('accountPermission')
      window.location.href = '/login'
      return Promise.reject(error)
    }
    message.error(error.message || '请求异常')
    return Promise.reject(error)
  }
)

export default request

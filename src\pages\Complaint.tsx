import React, { useState, useEffect } from 'react';
import apiClient from '../api/api';
import { Table, Pagination, Spin, message } from 'antd';


const Complaint = () => {
  const [loading, setLoading] = useState(false);
  const [complaints, setComplaints] = useState([]);
  const [total, setTotal] = useState(0);  // 总记录数
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(8);

  // 获取投诉记录的函数
  const fetchComplaints = async (page: number = 1, size: number = 8) => {
    setLoading(true);
    try {
      const response = await apiClient.get<any>(`complaint/getComplaint`, {
          page: page,
          size: size
      });
      if (response.code === 200) {
        setComplaints(response.data.records);  // 设置表格数据
        setTotal(response.data.total);  // 设置总记录数
      } else {
        message.error(response.data.message || "获取投诉记录失败");
      }
    } catch (error) {
      message.error("请求失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 切换页码
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchComplaints(page, pageSize);
  };

  // 切换每页记录数
  const handlePageSizeChange = (current: number, size: number) => {
    setPageSize(size);
    fetchComplaints(current, size);
  };

  useEffect(() => {
    fetchComplaints(currentPage, pageSize);
  }, [currentPage, pageSize]);

  // 表格列定义
  const columns = [
    {
      title: '投诉ID',
      dataIndex: 'complaintId',
      key: 'complaintId',
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '阿姨姓名',
      dataIndex: 'caregiverName',
      key: 'caregiverName',
    },
    {
      title: '投诉类型',
      dataIndex: 'complaintTypesStr',
      key: 'complaintTypesStr',
    },
    {
      title: '投诉内容',
      dataIndex: 'complaintDetail',
      key: 'complaintDetail',
    },
    {
      title: '反馈',
      dataIndex: 'feedback',
      key: 'feedback',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
    }
  ];

  return (
    <div style={{ padding: 20 }}>
      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={complaints}
          rowKey="complaintId"
          pagination={false}  // 不使用默认分页
        />
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={total}
          onChange={handlePageChange}
          onShowSizeChange={handlePageSizeChange}
          showSizeChanger
          pageSizeOptions={['8', '16', '24', '32']}
          style={{ marginTop: 20 }}
        />
      </Spin>
    </div>
  );
};

export default Complaint;

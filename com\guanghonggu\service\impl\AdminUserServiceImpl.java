@Override
public Map<String, Object> login(AdminUserDTO dto) {
    // 从数据库中查询用户信息
    AdminUser user = adminUserMapper.selectOne(
            new QueryWrapper<AdminUser>().eq("username", dto.getUsername()));

    // 如果用户不存在或密码错误，则抛出异常
    if (user == null || !user.getPassword().equals(dto.getPassword())) {
        throw new RuntimeException("用户名或密码错误");
    }

    // 使用 Sa-Token 登录并生成 token
    StpUtil.login(user.getId());

    // 将用户权限信息存储到 session 中
    StpUtil.getSession().set("accountPermission", user.getAccountPermission());
    StpUtil.getSession().set("username", user.getUsername());

    // 返回 Sa-Token 标准格式，包含用户ID
    Map<String, Object> result = new HashMap<>();
    result.put("tokenName", StpUtil.getTokenName());
    result.put("tokenValue", StpUtil.getTokenValue());
    result.put("accountPermission", user.getAccountPermission());
    result.put("id", user.getId()); // 添加用户ID

    return result;
}

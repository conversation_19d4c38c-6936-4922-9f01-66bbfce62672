import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Select, Upload, Space } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { addQA, importQA, type AddQAParams, fetchCategories } from '../api/qaApi';

const { TextArea } = Input;
const { Option } = Select;

interface Category {
  id: number;
  name: string;
}

const AddQAForm: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [form] = Form.useForm();
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    fetchCategories().then((res) => {
      if (res.code === 200) {
        setCategories(res.data);
      } else {
        message.error('分类获取失败：' + res.message);
      }
    });
  }, []);

  const handleSubmit = async (values: AddQAParams) => {
    setLoading(true);
    try {
      const res = await addQA(values);
      if (res.code === 200) {
        message.success('🎉 QA 添加成功');
        form.resetFields();
      } else {
        message.error(res.message || '添加失败');
      }
    } catch (err) {
      console.error(err);
      message.error('接口请求失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (file: File): Promise<boolean> => {
    setUploading(true);
    try {
      const res = await importQA(file);
      if (res.code === 200) {
        message.success('📦 批量导入成功');
      } else {
        message.error(res.message || '导入失败');
      }
    } catch (err) {
      console.error(err);
      message.error('上传失败');
    } finally {
      setUploading(false);
    }
    return false;
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      style={{ maxWidth: 600, margin: '0 auto', marginTop: 50 }}
    >
      <Form.Item
        label="问题"
        name="question"
        rules={[{ required: true, message: '请输入问题' }]}
      >
        <Input placeholder="输入问题" />
      </Form.Item>

      <Form.Item
        label="回答"
        name="answer"
        rules={[{ required: true, message: '请输入回答' }]}
      >
        <TextArea rows={4} placeholder="输入回答" />
      </Form.Item>

      <Form.Item
        label="分类"
        name="categoryId"
        rules={[{ required: true, message: '请选择分类' }]}
      >
        <Select placeholder="选择分类" loading={!categories.length}>
          {categories.map((cat) => (
            <Option key={cat.id} value={cat.id}>
              {cat.name}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            提交
          </Button>

          <Upload
            showUploadList={false}
            beforeUpload={handleUpload}
          >
            <Button icon={<UploadOutlined />} loading={uploading}>
              上传 Excel
            </Button>
          </Upload>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default AddQAForm;

import React, { useState, useEffect } from "react";
import {
  Table,
  Button,
  message,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  Space,
  Pagination,
  Card,
} from "antd";
import { PlusOutlined, UploadOutlined } from "@ant-design/icons";
import {
  addQA,
  importQA,
  fetchCategories,
  fetchQAList,
  type QAItem,
  type Category,
  type AddQAParams,
} from "../api/qaApi";

const { Option } = Select;
const { TextArea } = Input;

const QAListPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [qaList, setQaList] = useState<QAItem[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [selectedCategory, setSelectedCategory] = useState<number | undefined>();

  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  const [form] = Form.useForm();

  // 初始化加载分类和 QA 列表
  useEffect(() => {
    loadCategories();
    loadQAList();
  }, []);

const loadCategories = async () => {
  try {
    const res = await fetchCategories(); // axios 返回 { data, status, ... }
    if (res.code === 200) {
      setCategories(res.data.data); // data.data 是 Category[]
    } else {
      message.error("获取分类失败：" + res.message);
    }
  } catch (err) {
    console.error(err);
    message.error("获取分类异常");
  }
};

  const loadQAList = async (
    page = 1,
    size = 10,
    categoryId?: number
  ) => {
    try {
      console.log(categoryId);
      const res = await fetchQAList(page, size, categoryId?.toString());
      if (res.code === 200) {
        setQaList(res.data.data.records);
        setPagination({
          current: res.data.current,
          pageSize: res.data.size,
          total: res.data.total,
        });
      } else {
        message.error("获取 QA 列表失败：" + res.message);
      }
    } catch (err) {
      console.error(err);
      message.error("获取 QA 列表异常");
    }
  };

  const handleCategoryChange = (value?: number) => {
    setSelectedCategory(value);
    loadQAList(1, pagination.pageSize, value);
  };

  const handleAdd = () => {
    form.resetFields();
    setModalVisible(true);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      const res = await addQA(values as AddQAParams);
      if (res.code === 200) {
        message.success("🎉 QA 添加成功");
        setModalVisible(false);
        loadQAList(pagination.current, pagination.pageSize, selectedCategory);
      } else {
        message.error(res.message || "添加失败");
      }
    } catch (err) {
      console.error(err);
      message.error("表单验证或请求失败");
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (file: File): Promise<boolean> => {
    setUploading(true);
    try {
      const res = await importQA(file);
      if (res.code === 200) {
        message.success("📦 批量导入成功");
        loadQAList(pagination.current, pagination.pageSize, selectedCategory);
      } else {
        message.error(res.message || "导入失败");
      }
    } catch (err) {
      console.error(err);
      message.error("上传失败");
    } finally {
      setUploading(false);
    }
    return false;
  };

  return (
    <Card title="QA 问答管理" style={{ margin: 24 }}>
      {/* 操作栏 */}
      <Space style={{ marginBottom: 16 }}>
        <Select
          style={{ width: 200 }}
          placeholder="筛选分类"
          allowClear
          onChange={handleCategoryChange}
        >
          {categories.map((cat) => (
            <Option key={cat.categoryId} value={cat.categoryId}>
              {cat.categoryName}
            </Option>
          ))}
        </Select>

        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
          添加 QA
        </Button>

        <Upload showUploadList={false} beforeUpload={handleUpload}>
          <Button icon={<UploadOutlined />} loading={uploading}>
            上传 Excel
          </Button>
        </Upload>
      </Space>

      {/* QA 表格 */}
      <Table
        dataSource={qaList}
        rowKey={(record) => record.question + record.answer}
        pagination={false}
        columns={[
          { title: "分类", dataIndex: "categoryName", key: "categoryName" },
          { title: "问题", dataIndex: "question", key: "question" },
          { title: "回答", dataIndex: "answer", key: "answer" },
          
        ]}
      />

      {/* 分页 */}
      <Pagination
        style={{ marginTop: 16, textAlign: "right" }}
        current={pagination.current}
        pageSize={pagination.pageSize}
        total={pagination.total}
        onChange={(page, size) => loadQAList(page, size, selectedCategory)}
      />

      {/* 添加 QA 弹窗 */}
      <Modal
        title="添加 QA"
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
        okText="提交"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="问题"
            name="question"
            rules={[{ required: true, message: "请输入问题" }]}
          >
            <Input placeholder="输入问题" />
          </Form.Item>

          <Form.Item
            label="回答"
            name="answer"
            rules={[{ required: true, message: "请输入回答" }]}
          >
            <TextArea rows={4} placeholder="输入回答" />
          </Form.Item>

          <Form.Item
            label="分类"
            name="categoryId"
            rules={[{ required: true, message: "请选择分类" }]}
          >
            <Select placeholder="选择分类">
              {categories.map((cat) => (
                <Option key={cat.categoryId} value={cat.categoryId}>
                  {cat.categoryName}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default QAListPage;

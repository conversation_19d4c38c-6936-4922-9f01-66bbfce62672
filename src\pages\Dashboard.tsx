import React, { useEffect, useState } from 'react'
import { <PERSON>, Row, Col, <PERSON>ati<PERSON>, Button, Input } from 'antd'
import { Checkbox } from 'antd'
import {
  getCaregiverTotal,
  getUserTotal,
  getTotalIncomeStats,
  getTotalProfitStats,
  getTotalExpenseStats,
  getOrderTypeCounts,
  getRegionOrderTypeCount,
  getToolIncome,
  getTotalPlatformProfit,
  getOtherCompanyOrderAmount,
} from '../api/stats'
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, CartesianGrid } from 'recharts'
import IncomeBarCharts from './IncomeBarCharts'

const Dashboard: React.FC = () => {
  const [caregiverCount, setCaregiverCount] = useState(0)
  const [userCount, setUserCount] = useState(0)
  const [orderIncome, setOrderIncome] = useState(0)
  const [rechargeIncome, setRechargeIncome] = useState(0)
  const [totalIncome, setTotalIncome] = useState(0)
  const [orderExpense, setOrderExpense] = useState(0)
  const [withdrawExpense, setWithdrawExpense] = useState(0)
  const [totalExpense, setTotalExpense] = useState(0)
  const [orderProfit, setOrderProfit] = useState(0)
  const [rechargeProfit, setRechargeProfit] = useState(0)
  const [totalProfit, setTotalProfit] = useState(0)
  const [toolIncome, setToolIncome] = useState(0)
  const [otherCompanyOrderAmount, setOtherCompanyOrderAmount] = useState(0)
  const [platformProfit, setPlatformProfit] = useState(0)

  const [regionChartData, setRegionChartData] = useState<{ regionName: string; count: number }[]>([])
  const [area, setArea] = useState<string>('天津市西青区')
  const [serviceTypeIds, setServiceTypeIds] = useState<number[]>([1, 2, 3, 4, 5]) // 默认 [1..5]

  const serviceTypeMap: Record<number, string> = {
    1: '普通保洁',
    2: '开荒保洁',
    3: '玻璃清洗',
    4: '机器拆洗',
    5: '企业保洁',
  }

  const [typeChartData, setTypeChartData] = useState<{ name: string; count: number }[]>([])

  useEffect(() => {
    // 延迟执行，确保 token 已设置
    const delay = setTimeout(() => {
      const token = localStorage.getItem('token')
      if (token) {
        const cleanup = loadData()
        // 在卸载时清理 interval
        return () => cleanup?.()
      }
    }, 200)

    return () => clearTimeout(delay)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // —— 修复点：把“其他平台阿姨收入”的获取从 fetchRegionChart 中挪到组件作用域，并在 loadData 里调用 ——
  const fetchOtherCompanyOrderAmount = async () => {
    try {
      const res = await getOtherCompanyOrderAmount()
      // 兼容两种返回结构
      const val = (res && (res.otherCompanyOrderAmount ?? res.data?.otherCompanyOrderAmount)) || 0
      setOtherCompanyOrderAmount(Number(val) || 0)
    } catch (error) {
      console.error('获取其他平台阿姨收入失败:', error)
    }
  }

  // —— 修复点：loadToolIncome 内部调用 getToolIncome，这里只调用它本身即可 ——
  const loadToolIncome = async () => {
    try {
      const res = await getToolIncome()
      const val = (res && (res.toolIncome ?? res.data?.toolIncome)) || 0
      setToolIncome(Number(val) || 0)
    } catch (error) {
      console.error('获取工具收入失败:', error)
    }
  }

  const loadData = () => {
    const fetchAllStats = async () => {
      try {
        // 并发请求，减少等待时间
        const [caregivers, users, incomeStats, expenseStats, profitStats, orderTypeCounts] = await Promise.all([
          getCaregiverTotal(),
          getUserTotal(),
          getTotalIncomeStats(),
          getTotalExpenseStats(),
          getTotalProfitStats(),
          getOrderTypeCounts(),
        ])

        setCaregiverCount(Number(caregivers?.count ?? caregivers ?? 0))
        setUserCount(Number(users?.count ?? users ?? 0))

        if (incomeStats) {
          setOrderIncome(Number(incomeStats.orderIncome ?? incomeStats.data?.orderIncome ?? 0))
          setRechargeIncome(Number(incomeStats.rechargeIncome ?? incomeStats.data?.rechargeIncome ?? 0))
          setTotalIncome(Number(incomeStats.totalIncome ?? incomeStats.data?.totalIncome ?? 0))
        }

        if (expenseStats) {
          setOrderExpense(Number(expenseStats.orderExpense ?? expenseStats.data?.orderExpense ?? 0))
          setWithdrawExpense(Number(expenseStats.withdrawExpense ?? expenseStats.data?.withdrawExpense ?? 0))
          setTotalExpense(Number(expenseStats.totalExpense ?? expenseStats.data?.totalExpense ?? 0))
        }

        if (profitStats) {
          setOrderProfit(Number(profitStats.orderProfit ?? profitStats.data?.orderProfit ?? 0))
          setRechargeProfit(Number(profitStats.rechargeProfit ?? profitStats.data?.rechargeProfit ?? 0))
          setTotalProfit(Number(profitStats.totalProfit ?? profitStats.data?.totalProfit ?? 0))
        }

        if (Array.isArray(orderTypeCounts)) {
          const mapped = orderTypeCounts.map((item: any) => ({
            name: serviceTypeMap[item.serviceTypeId] || `类型${item.serviceTypeId}`,
            count: Number(item.count) || 0,
          }))
          setTypeChartData(mapped)
        }

        // 其他单项指标
        await Promise.all([
          (async () => {
            const res = await getTotalPlatformProfit()
            const val = (res && (res.platformProfit ?? res.data ?? res.data?.platformProfit)) || 0
            setPlatformProfit(Number(val) || 0)
          })(),
          loadToolIncome(),
          fetchOtherCompanyOrderAmount(),
        ])
      } catch (e) {
        console.error('统计数据拉取失败:', e)
      }
    }

    // 首次拉取
    fetchAllStats()
    // 每 5 分钟刷新一次
    const timer = setInterval(fetchAllStats, 300000)
    return () => clearInterval(timer)
  }

  // 地区 + 服务类型分布
  const fetchRegionChart = async () => {
    if (!area || serviceTypeIds.length === 0) return

    const fetchPromises = serviceTypeIds.map((serviceTypeId) =>
      getRegionOrderTypeCount({ area: String(area), serviceTypeId }).catch((err) => {
        console.error(`查询 ${serviceTypeId} 时出错:`, err)
        return []
      })
    )

    try {
      const responses = await Promise.all(fetchPromises)
      const mergedData = responses.flat()
      setRegionChartData(
        mergedData.map((d: any) => ({
          regionName: String(d.regionName ?? d.name ?? ''),
          count: Number(d.count) || 0,
        }))
      )
    } catch (err) {
      console.error('查询区域数据时出错:', err)
    }
  }

  const renderCard = (title: string, value: number, type: 'money' | 'count') => (
    <Col xs={24} sm={12} md={8} lg={6}>
      <Card style={{ minHeight: 100 }}>
        <Statistic title={title} value={Number(value) || 0} precision={type === 'money' ? 2 : 0} suffix={type === 'money' ? '元' : '人'} />
      </Card>
    </Col>
  )

  return (
    <div style={{ padding: 24 }}>
      <h2 style={{ fontSize: 24, fontWeight: 600, marginBottom: 24 }}>企业后台 - 数据看板</h2>

      <Row gutter={[16, 16]}>
        {renderCard('阿姨总数', caregiverCount, 'count')}
        {renderCard('用户总数', userCount, 'count')}
        {renderCard('订单收入', orderIncome, 'money')}
        {renderCard('充值收入', rechargeIncome, 'money')}
        {renderCard('平台总收入', totalIncome, 'money')}
        {renderCard('阿姨劳务所得', orderExpense, 'money')}
        {renderCard('提现支出', withdrawExpense, 'money')}
        {renderCard('订单收益', orderProfit, 'money')}
        {renderCard('工具收入', toolIncome, 'money')}
        {renderCard('平台总利润', platformProfit, 'money')}
        {renderCard('其他平台阿姨收入', otherCompanyOrderAmount, 'money')}
      </Row>

      <IncomeBarCharts />

      {typeChartData.length > 0 && (
        <Card style={{ marginTop: 40 }}>
          <h3>📊 各服务类型订单数量统计</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={typeChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" barSize={40} fill="#1890ff" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      )}

      <Card style={{ marginTop: 40 }}>
        <h3>📊 地区服务类型订单数量统计</h3>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <label>地区名称：</label>
            <Input value={area} onChange={(e) => setArea(e.target.value)} style={{ width: '100%' }} />
          </Col>
          <Col span={10}>
            <label>服务类型 ID：</label>
            <Checkbox.Group
              options={[1, 2, 3, 4, 5].map((id) => ({ label: serviceTypeMap[id], value: id }))}
              value={serviceTypeIds}
              onChange={(checkedValues) => setServiceTypeIds(checkedValues as number[])}
            />
          </Col>
          <Col span={8}>
            <Button type="primary" size="small" onClick={fetchRegionChart} style={{ marginTop: 22 }}>
              查询
            </Button>
          </Col>
        </Row>

        {regionChartData.length > 0 && (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={regionChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="regionName" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" barSize={40} fill="#52c41a" />
            </BarChart>
          </ResponsiveContainer>
        )}

        <Card size="small" style={{ marginTop: 16, backgroundColor: '#f9f9f9' }}>
          <Row gutter={[16, 8]}>
            {Object.entries(serviceTypeMap).map(([id, name]) => (
              <Col span={8} key={id}>
                <span
                  style={{
                    display: 'inline-block',
                    padding: '4px 8px',
                    backgroundColor: serviceTypeIds.includes(Number(id)) ? '#1890ff' : '#d9d9d9',
                    color: serviceTypeIds.includes(Number(id)) ? 'white' : '#666',
                    borderRadius: '4px',
                    fontSize: '12px',
                  }}
                >
                  {id}. {name}
                </span>
              </Col>
            ))}
          </Row>
        </Card>
      </Card>
    </div>
  )
}

export default Dashboard

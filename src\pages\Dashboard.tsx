import React, { useEffect, useState } from 'react'
import { Card, Row, Col, <PERSON>atistic, Button, Input } from 'antd'
import { Checkbox } from 'antd';
import {
  getCaregiverTotal,
  getUserTotal,
  getTotalIncomeStats,
  getTotalProfitStats,
  getTotalExpenseStats,
  getOrderTypeCounts,
  getRegionOrderTypeCount,
  getToolIncome,
  getTotalPlatformProfit
} from '../api/stats'
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, CartesianGrid } from 'recharts'
import IncomeBarCharts from './IncomeBarCharts'

const Dashboard: React.FC = () => {
  const [caregiverCount, setCaregiverCount] = useState(0)
  const [userCount, setUserCount] = useState(0)
  const [orderIncome, setOrderIncome] = useState(0)
  const [rechargeIncome, setRechargeIncome] = useState(0)
  const [totalIncome, setTotalIncome] = useState(0)
  const [orderExpense, setOrderExpense] = useState(0)
  const [withdrawExpense, setWithdrawExpense] = useState(0)
  const [totalExpense, setTotalExpense] = useState(0)
  const [orderProfit, setOrderProfit] = useState(0)
  const [rechargeProfit, setRechargeProfit] = useState(0)
  const [totalProfit, setTotalProfit] = useState(0)
  const [toolIncome, setToolIncome] = useState(0)
  const [platformProfit, setPlatformProfit] = useState(0)

  const [regionChartData, setRegionChartData] = useState<{ regionName: string, count: number }[]>([])
  const [area, setArea] = useState<string>('天津市西青区');
  const [serviceTypeIds, setServiceTypeIds] = useState<number[]>([1, 2, 3, 4, 5]); // 默认值设置为 [1, 2, 3, 4, 5]
  const serviceTypeMap: Record<number, string> = {
    1: '普通保洁', 2: '开荒保洁', 3: '玻璃清洗', 4: '机器拆洗', 5: '企业保洁'
  }
  const [typeChartData, setTypeChartData] = useState<{ name: string, count: number }[]>([])

  useEffect(() => {
    // 延迟执行，确保 token 已设置
    const timer = setTimeout(() => {
      const token = localStorage.getItem('token')
      console.log('Dashboard 加载时的 token:', token)
      
      if (token) {
        loadData()
      } else {
        console.error('Dashboard: 未找到 token')
      }
    }, 200)
    
    return () => clearTimeout(timer)
  }, [])

  const loadData = () => {
    const fetchAllStats = () => {
      getCaregiverTotal().then(setCaregiverCount)
      getUserTotal().then(setUserCount)
      getTotalIncomeStats().then(res => {
        setOrderIncome(res.orderIncome)
        setRechargeIncome(res.rechargeIncome)
        setTotalIncome(res.totalIncome)
      })
      getTotalExpenseStats().then(res => {
        setOrderExpense(res.orderExpense)
        setWithdrawExpense(res.withdrawExpense)
        setTotalExpense(res.totalExpense)
      })
      getTotalProfitStats().then(res => {
        setOrderProfit(res.orderProfit)
        setRechargeProfit(res.rechargeProfit)
        setTotalProfit(res.totalProfit)
      })
      getOrderTypeCounts().then(res => {
        const mapped = res.map((item:any) => ({
          name: serviceTypeMap[item.serviceTypeId] || `类型${item.serviceTypeId}`,
          count: item.count
        }))
        setTypeChartData(mapped)
      })
    }

    fetchAllStats()
    loadToolIncome().then(getToolIncome) 

    fetchAllStats()
    // 获取平台总利润
    getTotalPlatformProfit().then(setPlatformProfit)

    fetchAllStats()
    const timer = setInterval(fetchAllStats, 300000)
    return () => clearInterval(timer)
  }

  // 获取工具收入
  const loadToolIncome = async () => {
    try {
      const response = await getToolIncome()
      setToolIncome(response.toolIncome)  // 假设接口返回的数据结构中包含 toolIncome 字段
    } catch (error) {
      console.error("获取工具收入失败:", error)
    }
  }

  //获取平台总收益

  const fetchRegionChart = () => {
    if (area && serviceTypeIds.length > 0) {
      console.log("正在查询区域数据...", serviceTypeIds);  // 确认 serviceTypeIds 的值
  
      const fetchPromises = serviceTypeIds.map(serviceTypeId => 
        getRegionOrderTypeCount({
          area: area.toString(),
          serviceTypeId: serviceTypeId
        }).catch(err => {
          console.error(`查询 ${serviceTypeId} 时出错:`, err);
          return []; // 请求失败时返回空数组
        })
      );
  
      // 等待所有请求完成
      Promise.all(fetchPromises)
        .then(responses => {
          // 合并所有响应数据
          const mergedData = responses.flat();
  
          if (mergedData.length === 0) {
            console.warn("没有返回数据");
          } else {
            console.log("合并后的数据:", mergedData);  // 打印合并后的数据
          }
  
          // 更新图表数据
          setRegionChartData(mergedData);
        })
        .catch(err => {
          console.error("查询区域数据时出错:", err);
        });
    }
  };
  

  const renderCard = (title: string, value: number, type: 'money' | 'count') => (
    <Col xs={24} sm={12} md={8} lg={6}>
      <Card style={{ minHeight: 100 }}>
        <Statistic
          title={title}
          value={value}
          precision={type === 'money' ? 2 : 0}
          suffix={type === 'money' ? '元' : '人'}
        />
      </Card>
    </Col>
  )

  return (
    <div style={{ padding: 24 }}>
      <h2 style={{ fontSize: 24, fontWeight: 600, marginBottom: 24 }}>企业后台 - 数据看板</h2>

      <Row gutter={[16, 16]}>
        {renderCard('阿姨总数', caregiverCount, 'count')}
        {renderCard('用户总数', userCount, 'count')}
        {renderCard('订单收入', orderIncome, 'money')}
        {renderCard('充值收入', rechargeIncome, 'money')}
        {renderCard('平台总收入', totalIncome, 'money')}
        {renderCard('阿姨劳务所得', orderExpense, 'money')}
        {renderCard('提现支出', withdrawExpense, 'money')}
        {renderCard('订单收益', orderProfit, 'money')}
        {renderCard('工具收入', toolIncome, 'money')}
        {renderCard('平台总利润', platformProfit, 'money')}
      </Row>

      <IncomeBarCharts />

      {typeChartData.length > 0 && (
        <Card style={{ marginTop: 40 }}>
          <h3>📊 各服务类型订单数量统计</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={typeChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" barSize={40} fill="#1890ff" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      )}

      <Card style={{ marginTop: 40 }}>
        <h3>📊 地区服务类型订单数量统计</h3>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <label>地区名称：</label>
            <Input 
              value={area}
              onChange={e => setArea(e.target.value)}
              style={{ width: '100%' }} 
            />
          </Col>
          <Col span={6}>
            <label>服务类型 ID：  </label>
            <Checkbox.Group 
              options={[1, 2, 3, 4, 5].map(id => ({ label: serviceTypeMap[id], value: id }))}
              value={serviceTypeIds}
              onChange={checkedValues => setServiceTypeIds(checkedValues as number[])}
            />
          </Col>
          <Col span={6}>
            <Button 
              type="primary" 
              size="small" 
              onClick={fetchRegionChart} 
              style={{ marginTop: 22 }}
            >
              查询
            </Button>
          </Col>
        </Row>

        {/* 图表区域 */}
        {regionChartData.length > 0 && (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={regionChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="regionName" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" barSize={40} fill="#52c41a" />
            </BarChart>
          </ResponsiveContainer>
        )}

        {/* 服务类型说明 */}
        <Card 
          size="small" 
          style={{ marginTop: 16, backgroundColor: '#f9f9f9' }}
        >
          <Row gutter={[16, 8]}>
            {Object.entries(serviceTypeMap).map(([id, name]) => (
              <Col span={8} key={id}>
                <span style={{ 
                  display: 'inline-block',
                  padding: '4px 8px',
                  backgroundColor: serviceTypeIds.includes(Number(id)) ? '#1890ff' : '#d9d9d9',
                  color: serviceTypeIds.includes(Number(id)) ? 'white' : '#666',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  {id}. {name}
                </span>
              </Col>
            ))}
          </Row>
        </Card>
      </Card>
    </div>
  )
}

export default Dashboard

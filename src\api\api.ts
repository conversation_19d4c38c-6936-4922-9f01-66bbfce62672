
import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios';


// const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
// 定义基础API返回类型
interface ApiResponse<T = any> {
  config: any;
  code: number;
  message: string;
  data: T;
}

// 定义请求配置
interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean;
  errorModal?: boolean;
  data?: any;
}

class ApiClient {
  private instance: AxiosInstance;
  private baseUrl: string;

  constructor(baseUrl: string) {
    
    this.baseUrl = baseUrl;
    this.instance = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json', // 发送json格式的数据
        'Accept': 'application/json', // 接受json格式的数据
        // 'token':localStorage.getItem('token') || ''
      },
    });

    // 请求拦截器
    this.instance.interceptors.request.use(
      async (config) => {
        // 自动添加 token，添加 Bearer 前缀
        const token = localStorage.getItem('token')
        if (token) {
          config.headers.token = `Bearer ${token}`  // 添加 Bearer 前缀
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        if(response.data.code==500){
          
        }
        
        console.log('Response:', response.data.code);
        return response;
      },
      (error) => {
        // 统一错误处理
        if (error.response) {
          // 处理 401 未授权错误
          if (error.response.status === 401) {
            console.error('Token 验证失败，跳转到登录页')
            localStorage.removeItem('token')
            localStorage.removeItem('accountPermission')
            window.location.href = '/login'
            return Promise.reject(error)
          }
          console.error('API Error:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('No response received:', error.request);
        } else {
          console.error('Request setup error:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * 通用请求方法
   * @param config 请求配置
   * @returns Promise<T>
   */
  public async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.instance(config);
      if (response.data.code !== 200) {
        throw new Error(response.data.message);
      }
      return response.data;
    } catch (error) {
      // 可以在这里添加更详细的错误处理逻辑
      throw error;
    }
  }

  /**
   * GET请求
   * @param url 请求地址
   * @param params 查询参数
   * @param config 请求配置
   * @returns Promise<T>
   */
  public async get<T>(url: string, params?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'GET',
      url,
      params,
      ...config,
    });
  }

  /**
   * POST请求
   * @param url 请求地址
   * @param data 请求体数据
   * @param config 请求配置
   * @returns Promise<ApiResponse<T>>
   */
  public async post<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url,
      data,
      ...config,
    });
  }

  /**
   * PUT请求
   * @param url 请求地址
   * @param data 请求体数据
   * @param config 请求配置
   * @returns Promise<ApiResponse<T>>
   */
  public async put<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PUT',
      url,
      data,
      ...config,
    });
  }

  /**
   * DELETE请求
   * @param url 请求地址
   * @param data 请求体数据
   * @param config 请求配置
   * @returns Promise<ApiResponse<T>>
   */
  public async delete<T>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'DELETE',
      url,
      data,
      ...config,
    });
  }
}

// 创建API实例 - 请替换为您的实际API基础地址
// const BASE_URL = 'http://192.168.1.67:8080/';
// const BASE_URL = 'http://qinqinjava.cn:8081/';
// const BASE_URL = 'http://192.168.0.11:8082/';
const BASE_URL = '/api';

const apiClient = new ApiClient(BASE_URL);

export default apiClient;



